<template>
  <TDrawer
    :title="infoMap[type].title"
    :visible.sync="visible"
    v-bind="$attrs"
    @close="handleClose"
    v-loading="loading"
  >
    <el-form
      ref="formRef"
      :rules="rules"
      :model="formState"
      label-width="130px"
      size="small"
      :disabled="disabled"
      :class="disabled ? 'height100' : ''"
    >
      <el-tabs
        class="smart_city__el-tabs smart_city__el-tabs-flex"
        v-model="activeTab"
        v-if="disabled"
      >
        <el-tab-pane label="目录信息" name="info">
          <el-container style="height: 100%">
            <el-scrollbar class="scrollbar-wrapper">
              <TitleInfo
                style="width: 100%"
                title="基本信息"
                :animation="true"
                v-model="titleActiveA"
              >
                <template #content>
                  <el-row type="flex" style="flex-wrap: wrap">
                    <el-col :span="12">
                      <el-form-item label="部门名称" prop="unitName">
                        <div style="display: flex; gap: 8px">
                          <el-input
                            v-model="formState.unitName"
                            :disabled="disabled"
                            placeholder="请选择目录所属部门"
                            readonly
                          />
                          <el-button
                            v-if="['add', 'edit', 'change'].includes(type)"
                            type="primary"
                            @click="handleChooseUnit"
                          >
                            选择
                          </el-button>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="目录名称" prop="directoryName">
                        <el-input
                          clearable
                          v-model.trim="formState.directoryName"
                          placeholder="请输入目录名称"
                          :maxlength="50"
                          :disabled="disabled"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col
                      :span="12"
                      v-if="
                        [
                          'publishView',
                          'catalogPublishAudit',
                          'publishChangeView',
                          'historyView',
                          'publishChangeAudit'
                        ].includes(type)
                      "
                    >
                      <el-form-item label="目录分类" prop="categoryName">
                        <el-input
                          @mouseenter.native="showCategory = true"
                          @mouseleave.native="showCategory = false"
                          v-model.trim="formState.categoryName"
                          placeholder="请选择目录分类"
                          readonly
                          :disabled="disabled"
                          @click.native="openDirectory(1)"
                        >
                          <i
                            v-show="
                              showCategory &&
                              formState.categoryName &&
                              !disabled
                            "
                            @click.stop="cleanCategory(1)"
                            slot="suffix"
                            class="el-icon-close"
                          ></i>
                        </el-input>
                      </el-form-item>
                    </el-col>
                    <el-col
                      :span="12"
                      v-if="
                        [
                          'publishView',
                          'catalogPublishAudit',
                          'publishChangeView',
                          'historyView',
                          'publishChangeAudit'
                        ].includes(type)
                      "
                    >
                      <el-form-item label="目录代码" prop="resourceCode">
                        <el-input
                          v-model.trim="formState.resourceCode"
                          disabled
                          placeholder=" "
                          :maxlength="50"
                        />
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item
                        class="fromSystemStyle"
                        label="所属系统"
                        prop="fromSystem"
                      >
                        <el-input v-model="formState.systemName" disabled />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="数据区域范围" prop="areaRange">
                        <SelectPlus
                          dictType="BM_AREA_RANGE"
                          v-model="formState.areaRange"
                          :disabled="disabled"
                          style="width: 100%"
                          :placeholder="disabled ? ' ' : '请选择数据区域范围'"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="应用场景" prop="useType">
                        <SelectPlus
                          dictType="BM_USE_TYPE"
                          v-model="formState.useType"
                          :disabled="disabled"
                          style="width: 100%"
                          :placeholder="disabled ? ' ' : '请选择应用场景'"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="所属领域" prop="domian">
                        <SelectPlus
                          dictType="BM_DOMAIN"
                          v-model="formState.domian"
                          clearable
                          :disabled="disabled"
                          style="width: 100%"
                          :placeholder="disabled ? ' ' : '请选择所属领域'"
                        />
                      </el-form-item>
                    </el-col>

                    <el-col
                      :span="12"
                      v-if="
                        [
                          'publishView',
                          'catalogPublishAudit',
                          'publishChangeView',
                          'historyView',
                          'publishChangeAudit'
                        ].includes(type)
                      "
                    >
                      <el-form-item label="负责人" prop="chargePerson">
                        <el-input
                          v-model="formState.chargePerson"
                          clearable
                          :placeholder="disabled ? '' : '请输入负责人'"
                          maxlength="20"
                          :disabled="disabled"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col
                      :span="12"
                      v-if="
                        [
                          'publishView',
                          'catalogPublishAudit',
                          'publishChangeView',
                          'historyView',
                          'publishChangeAudit'
                        ].includes(type)
                      "
                    >
                      <el-form-item label="负责人联系方式" prop=" createdPhone">
                        <el-input
                          v-model="formState.createdPhone"
                          clearable
                          :placeholder="disabled ? '' : '请输入负责人联系方式'"
                          maxlength="20"
                          :disabled="disabled"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="目录摘要" prop="cataAbstract">
                        <el-input
                          type="textarea"
                          style="width: 100%"
                          v-model="formState.cataAbstract"
                          :placeholder="disabled ? ' ' : '请输入目录摘要'"
                          clearable
                          :disabled="disabled"
                          :maxlength="150"
                          show-word-limit
                          :autosize="{ minRows: 3, maxRows: 3 }"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col
                      :span="24"
                      v-if="
                        [
                          'publishView',
                          'catalogPublishAudit',
                          'publishChangeView',
                          'historyView',
                          'publishChangeAudit'
                        ].includes(type)
                      "
                    >
                      <el-form-item
                        label-width="200px"
                        label="是否共享至政务大数据平台"
                        prop="isCascade"
                      >
                        <el-radio-group
                          :disabled="disabled"
                          v-model="formState.isCascade"
                        >
                          <el-radio label="1">是</el-radio>
                          <el-radio label="0">否</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </template>
              </TitleInfo>
              <TitleInfo
                v-if="formState.isCascade === '1'"
                style="width: 100%"
                title="共享信息"
                :animation="true"
                v-model="titleActiveC"
              >
                <template #content>
                  <el-row type="flex" style="flex-wrap: wrap">
                    <el-col :span="12">
                      <el-form-item label="来源事项名称" prop="sourceItemName">
                        <el-input
                          clearable
                          v-model="formState.sourceItemName"
                          :maxlength="50"
                          :placeholder="disabled ? ' ' : '请输入来源事项名称'"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="共享类型" prop="shardType">
                        <SelectPlus
                          dictType="BM_SHARD_TYPE"
                          v-model="formState.shardType"
                          clearable
                          style="width: 100%"
                          :disabled="disabled"
                          :placeholder="disabled ? ' ' : '请选择共享类型'"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="共享方式" prop="sharindMethod">
                        <SelectPlus
                          dictType="BM_SHARIND_METHOD"
                          v-model="formState.sharindMethod"
                          clearable
                          style="width: 100%"
                          :disabled="disabled"
                          :placeholder="disabled ? ' ' : '请选择共享方式'"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item
                        label="共享要求"
                        prop="sharindConditionArr"
                        v-if="formState.shardType === '2'"
                      >
                        <el-select
                          multiple
                          style="width: 100%"
                          v-model="formState.sharindConditionArr"
                          clearable
                          :disabled="disabled"
                          :placeholder="disabled ? ' ' : '请选择共享要求'"
                        >
                          <el-option
                            v-for="item in shardConditionOpt"
                            :label="item.label"
                            :value="item.value"
                            :key="item.value"
                          >
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item
                        label="共享要求"
                        prop="sharindCondition"
                        v-else
                      >
                        <el-input
                          v-model.trim="formState.sharindCondition"
                          placeholder="请输入共享要求"
                          :maxlength="60"
                          clearable
                          :disabled="disabled"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </template>
              </TitleInfo>
              <TitleInfo
                style="width: 100%"
                :animation="true"
                v-model="titleActiveB"
              >
                <template #title>
                  <div style="display: flex; align-items: flex-end">
                    信息项
                    <el-descriptions
                      v-if="
                        ![
                          'publishView',
                          'publishChangeView',
                          'catalogPublishAudit',
                          'historyView',
                          'publishChangeAudit'
                        ].includes(type)
                      "
                      title=""
                      :column="3"
                      :colon="false"
                      :labelStyle="{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '20px',
                        height: '20px'
                      }"
                      style="width: 240px; margin-left: 12px"
                    >
                      <el-descriptions-item>
                        <template slot="label">
                          <div
                            class="circel"
                            style="background-color: #1b00a7"
                          />
                        </template>
                        草稿态
                      </el-descriptions-item>
                      <el-descriptions-item>
                        <template slot="label">
                          <div
                            class="circel"
                            style="background-color: #606266"
                          />
                        </template>
                        发布态
                      </el-descriptions-item>
                      <el-descriptions-item>
                        <template slot="label">
                          <div class="circel" style="background-color: red" />
                        </template>
                        下线态
                      </el-descriptions-item>
                    </el-descriptions>
                  </div>
                </template>
                <template #content>
                  <div class="moduleInfo" v-if="formState.catalogGroup !== '0'">
                    <div class="leftTree">
                      <div class="leftTree-bottom">
                        <el-scrollbar
                          v-loading="treeLoading"
                          ref="scrollbarRef"
                          style="height: 100%"
                        >
                          <el-tree
                            :default-expand-all="true"
                            class="treeList"
                            :props="defaultProps"
                            :data="treeList"
                            :default-checked-keys="defaultSelectedKeys"
                            node-key="id"
                            check-strictly
                            highlight-current
                            :show-checkbox="
                              [
                                'publishView',
                                'publishChangeView',
                                'historyView',
                                'catalogPublishAudit',
                                'publishChangeAudit'
                              ].includes(type)
                            "
                            @node-click="handleNodeClick"
                            @check="handleNodeCheck"
                            @node-collapse="updateScrollbar"
                            @node-expand="updateScrollbar"
                            ref="inBoxTree"
                          >
                            <div
                              class="custom-tree-node"
                              slot-scope="{ node, data }"
                            >
                              <el-tooltip
                                class="item"
                                effect="dark"
                                :disabled="node.label.length < 6"
                                :content="node.label"
                                placement="right"
                              >
                                <div
                                  style="
                                    flex: 1;
                                    margin-right: 4px;
                                    width: 100px;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                  "
                                  :class="{
                                    deletedClass: data.isDel === '1',
                                    blueColor: data.statusCode === '0'
                                  }"
                                >
                                  {{ node.label }}
                                </div>
                              </el-tooltip>
                              <div>
                                <el-tag
                                  type="success"
                                  v-if="node.level === 1"
                                  size="mini"
                                  >模型</el-tag
                                >

                                <el-tag
                                  type="info"
                                  class="tag-info"
                                  v-if="node.level === 2 && data.entityTypeCode"
                                  size="mini"
                                  >{{ data.entityType }}</el-tag
                                >

                                <!-- <el-tag
                          @click.stop="handleSeletTable(data)"
                          type="success"
                          v-if="
                            node.level === 2 && data.entityCatalogCode === '1'
                          "
                          size="mini"
                          >业务表</el-tag
                        >

                        <el-tag
                          @click.stop="handleSeletTable(data)"
                          type="info"
                          class="tag-info"
                          v-if="
                            node.level === 2 && data.entityCatalogCode !== '1'
                          "
                          size="mini"
                          >非业务表</el-tag
                        > -->
                              </div>
                            </div>
                          </el-tree>
                        </el-scrollbar>
                      </div>
                    </div>
                    <div class="rightTable">
                      <TablePlus
                        v-if="selectedType === 'table'"
                        ref="tableRef"
                        :data="formState.list"
                        v-loading="infoItemLoading"
                        border
                        fit
                        stripe
                        highlight-current-row
                        @header-dragend="handleHeaderDrag"
                      >
                        <el-table-column
                          prop="itemName"
                          label="信息项名称"
                          min-width="140"
                          show-overflow-tooltip
                          fixed="left"
                        >
                        </el-table-column>
                        <el-table-column
                          v-if="formState.catalogGroup === '2'"
                          prop="itemCode"
                          label="编码"
                          width="140"
                          show-overflow-tooltip
                        >
                        </el-table-column>
                        <el-table-column
                          prop="dataTypeCode"
                          label="数据类型"
                          min-width="120"
                          align="center"
                          show-overflow-tooltip
                        >
                        </el-table-column>

                        <el-table-column
                          prop="dataLength"
                          label="数据长度"
                          width="120"
                          align="right"
                          show-overflow-tooltip
                        ></el-table-column>

                        <el-table-column
                          v-if="formState.catalogGroup === '1'"
                          prop="isRelCol"
                          label="是否关联字段"
                          min-width="120"
                          align="center"
                          show-overflow-tooltip
                        >
                          <template slot-scope="{ row }">
                            {{ row.isRelCol ? '是' : '否' }}
                          </template>
                        </el-table-column>

                        <el-table-column
                          v-else
                          prop="isbind"
                          label="是否注册资源"
                          min-width="120"
                          align="center"
                          show-overflow-tooltip
                        >
                          <template slot-scope="{ row }">
                            <span>
                              {{
                                row.isBind === '1'
                                  ? '是'
                                  : row.isBind === '-1'
                                  ? '已删除'
                                  : '否'
                              }}</span
                            >
                          </template>
                        </el-table-column>
                        <el-table-column
                          prop="itemDesc"
                          label="信息项描述"
                          width="180"
                          show-overflow-tooltip
                        >
                        </el-table-column>
                        <!-- <el-table-column
                  v-if="formState.cataType === '3' && disabled"
                  prop="isBusinessCol"
                  label="是否业务字段"
                  min-width="120"
                  align="center"
                  show-overflow-tooltip
                >
                  <template slot-scope="{ row }">
                    {{ row.isBusinessCol ? '是' : '否' }}
                  </template>
                </el-table-column> -->

                        <!-- <el-table-column
                  v-else
                  prop="isBiz"
                  label="是否业务字段"
                  min-width="120"
                  align="center"
                  show-overflow-tooltip
                >
                  <template slot-scope="{ row }">
                    <el-switch
                      v-if="['add', 'edit'].includes(type)"
                      v-model="row.isBiz"
                      @change="handleToggleSwitch('2', row)"
                    >
                    </el-switch>
                    <span v-else> {{ row.isBiz ? '是' : '否' }}</span>
                  </template>
                </el-table-column> -->
                        <el-table-column
                          v-if="
                            [
                              'publishView',
                              'catalogPublishAudit',
                              'publishChangeView',
                              'historyView',
                              'publishChangeAudit'
                            ].includes(type)
                          "
                          prop="isShare"
                          label="是否共享"
                          width="120"
                          align="center"
                          show-overflow-tooltip
                        >
                          <template slot-scope="{ row }">
                            <span> {{ row.isShare ? '是' : '否' }}</span>
                          </template>
                        </el-table-column>
                      </TablePlus>
                      <el-descriptions
                        title=""
                        border
                        :column="1"
                        :labelStyle="{ width: '100px' }"
                        v-if="selectedType !== 'table'"
                      >
                        <el-descriptions-item label="实体名称">{{
                          selectedObj.entityName
                        }}</el-descriptions-item>
                        <el-descriptions-item label="实体类型">
                          <el-tag size="mini" class="tag-info" type="info">{{
                            selectedObj.entityType
                          }}</el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="SQL">{{
                          selectedObj.sql
                        }}</el-descriptions-item>
                        <el-descriptions-item label="创建时间">{{
                          selectedObj.createTime
                        }}</el-descriptions-item>
                      </el-descriptions>
                    </div>
                  </div>
                  <div v-else class="infoItem">
                    <!--列表表格区-->
                    <TablePlus
                      ref="tableRef"
                      :data="formState.list"
                      v-loading="infoItemLoading"
                      border
                      fit
                      stripe
                      highlight-current-row
                      @header-dragend="handleHeaderDrag"
                    >
                      <el-table-column
                        prop="itemName"
                        label="信息项名称"
                        min-width="180"
                        fixed="left"
                        show-overflow-tooltip
                      >
                      </el-table-column>
                      <el-table-column
                        prop="dataTypeCode"
                        label="数据类型"
                        min-width="130"
                        align="center"
                        show-overflow-tooltip
                      >
                      </el-table-column>

                      <el-table-column
                        prop="dataLength"
                        label="数据长度"
                        min-width="130"
                        align="right"
                        show-overflow-tooltip
                      ></el-table-column>

                      <el-table-column
                        prop="itemDesc"
                        label="信息项描述"
                        min-width="180"
                        show-overflow-tooltip
                      >
                      </el-table-column>
                      <el-table-column
                        v-if="!disabled"
                        label="操作"
                        align="center"
                        fixed="right"
                        header-align="center"
                        min-width="180"
                      >
                        <template slot-scope="scope">
                          <perm-button-group
                            :config="getInfoItemButtons(scope)"
                          />
                        </template>
                      </el-table-column>
                    </TablePlus>
                  </div>
                </template>
              </TitleInfo>
            </el-scrollbar>
          </el-container>
        </el-tab-pane>
        <el-tab-pane label="审批进度" name="process">
          <el-container style="height: 100%">
            <el-scrollbar class="scrollbar-wrapper">
              <ProgressView
                v-if="activeTab === 'process'"
                ref="progressRef"
                :auditRecord="auditRecord"
              />
            </el-scrollbar>
          </el-container>
        </el-tab-pane>
      </el-tabs>

      <template v-else>
        <TitleInfo
          style="width: 100%"
          title="基本信息"
          :animation="true"
          v-model="titleActiveA"
        >
          <template #content>
            <el-row type="flex" style="flex-wrap: wrap">
              <el-col :span="12">
                <el-form-item label="部门名称" prop="unitName">
                  <div style="display: flex; gap: 8px">
                    <el-input
                      v-model="formState.unitName"
                      :disabled="disabled"
                      placeholder="请选择目录所属部门"
                      readonly
                    />
                    <el-button
                      v-if="['add', 'edit', 'change'].includes(type)"
                      type="primary"
                      @click="handleChooseUnit"
                    >
                      选择
                    </el-button>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="目录名称" prop="directoryName">
                  <el-input
                    clearable
                    v-model.trim="formState.directoryName"
                    placeholder="请输入目录名称"
                    :maxlength="50"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
              <el-col
                :span="12"
                v-if="
                  ['publish', 'publishEdit', 'publishChange'].includes(type)
                "
              >
                <el-form-item label="目录分类" prop="categoryName">
                  <el-input
                    @mouseenter.native="showCategory = true"
                    @mouseleave.native="showCategory = false"
                    v-model.trim="formState.categoryName"
                    placeholder="请选择目录分类"
                    readonly
                    :disabled="disabled"
                    @click.native="openDirectory(1)"
                  >
                    <i
                      v-show="
                        showCategory && formState.categoryName && !disabled
                      "
                      @click.stop="cleanCategory(1)"
                      slot="suffix"
                      class="el-icon-close"
                    ></i>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col
                :span="12"
                v-if="
                  ['publish', 'publishEdit', 'publishChange'].includes(type)
                "
              >
                <el-form-item label="目录代码" prop="resourceCode">
                  <el-input
                    v-model.trim="formState.resourceCode"
                    disabled
                    placeholder="根据已选择的目录分类自动生成"
                    :maxlength="50"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  class="fromSystemStyle"
                  label="所属系统"
                  prop="fromSystem"
                >
                  <div style="display: flex; gap: 8px">
                    <el-select
                      ref="selectRef"
                      v-model="formState.fromSystem"
                      placeholder="请选择所属系统"
                      filterable
                      style="width: 100%"
                      clearable
                      :disabled="isDisabledSystem"
                      :popper-append-to-body="false"
                      popper-class="fromSystemSelect"
                      :loading="searchLoad"
                      :filter-method="filterMethod"
                      v-loadmore="loadMore(rangeNumber)"
                      @visible-change="visibleChange"
                      @change="handleSystemChange"
                    >
                      <el-tooltip
                        v-for="item in fromSystemList.slice(0, rangeNumber)"
                        :key="item.systemId"
                        effect="dark"
                        :content="item.systemName"
                        :open-delay="500"
                        placement="top-start"
                      >
                        <el-option
                          :disabled="item.enableFlag !== '1'"
                          :label="item.systemName"
                          :value="item.systemId"
                        >
                        </el-option>
                      </el-tooltip>
                    </el-select>
                    <!-- <el-button type="primary" @click="systemAdd">
                      新增
                    </el-button> -->
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="数据区域范围" prop="areaRange">
                  <SelectPlus
                    dictType="BM_AREA_RANGE"
                    v-model="formState.areaRange"
                    :disabled="disabled"
                    style="width: 100%"
                    :placeholder="disabled ? ' ' : '请选择数据区域范围'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="应用场景" prop="useType">
                  <SelectPlus
                    dictType="BM_USE_TYPE"
                    v-model="formState.useType"
                    :disabled="disabled"
                    style="width: 100%"
                    :placeholder="disabled ? ' ' : '请选择应用场景'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属领域" prop="domian">
                  <SelectPlus
                    dictType="BM_DOMAIN"
                    v-model="formState.domian"
                    clearable
                    :disabled="disabled"
                    style="width: 100%"
                    :placeholder="disabled ? ' ' : '请选择所属领域'"
                  />
                </el-form-item>
              </el-col>
              <!-- <el-col
              :span="12"
            >
              <el-form-item label="标签" prop="labelName">
                <el-input
                  v-model.trim="formState.labelName"
                  :placeholder="disabled ? ' ' : '多个标签用英文逗号间隔'"
                  :disabled="disabled"
                  :maxlength="50"
                  clearable
                />
              </el-form-item>
            </el-col> -->
              <el-col
                :span="12"
                v-if="
                  ['publish', 'publishEdit', 'publishChange'].includes(type)
                "
              >
                <el-form-item label="负责人" prop="chargePerson">
                  <el-input
                    v-model="formState.chargePerson"
                    clearable
                    :placeholder="disabled ? '' : '请输入负责人'"
                    maxlength="20"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
              <el-col
                :span="12"
                v-if="
                  ['publish', 'publishEdit', 'publishChange'].includes(type)
                "
              >
                <el-form-item label="负责人联系方式" prop=" createdPhone">
                  <el-input
                    v-model="formState.createdPhone"
                    clearable
                    :placeholder="disabled ? '' : '请输入负责人联系方式'"
                    maxlength="20"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="目录摘要" prop="cataAbstract">
                  <el-input
                    type="textarea"
                    style="width: 100%"
                    v-model="formState.cataAbstract"
                    :placeholder="disabled ? ' ' : '请输入目录摘要'"
                    clearable
                    :disabled="disabled"
                    :maxlength="150"
                    show-word-limit
                    :autosize="{ minRows: 3, maxRows: 3 }"
                  />
                </el-form-item>
              </el-col>
              <el-col
                :span="12"
                v-if="
                  ['publish', 'publishEdit', 'publishChange'].includes(type)
                "
              >
                <el-form-item
                  label-width="200px"
                  label="是否共享至政务大数据平台"
                  prop="isCascade"
                >
                  <el-radio-group
                    :disabled="disabled"
                    v-model="formState.isCascade"
                  >
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>

              <el-col
                :span="12"
                v-if="['publish'].includes(type) && isShowCatalogTypeSwitch"
              >
                <el-form-item label="目录类型" prop="catalogGroup">
                  <el-radio-group
                    v-model="formState.catalogGroup"
                    @input="handleChangeCatalogGroup"
                  >
                    <el-radio label="2">数据库目录</el-radio>
                    <el-radio label="0">政务数据目录</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </TitleInfo>
        <TitleInfo
          v-if="formState.isCascade === '1'"
          style="width: 100%"
          title="共享信息"
          :animation="true"
          v-model="titleActiveC"
        >
          <template #content>
            <el-row type="flex" style="flex-wrap: wrap">
              <el-col :span="12">
                <el-form-item label="来源事项名称" prop="sourceItemName">
                  <el-input
                    clearable
                    v-model="formState.sourceItemName"
                    :maxlength="50"
                    :placeholder="disabled ? ' ' : '请输入来源事项名称'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="共享类型" prop="shardType">
                  <SelectPlus
                    dictType="BM_SHARD_TYPE"
                    v-model="formState.shardType"
                    clearable
                    style="width: 100%"
                    :disabled="disabled"
                    :placeholder="disabled ? ' ' : '请选择共享类型'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="共享方式" prop="sharindMethod">
                  <SelectPlus
                    dictType="BM_SHARIND_METHOD"
                    v-model="formState.sharindMethod"
                    clearable
                    style="width: 100%"
                    :disabled="disabled"
                    :placeholder="disabled ? ' ' : '请选择共享方式'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="共享要求"
                  prop="sharindConditionArr"
                  v-if="formState.shardType === '2'"
                >
                  <el-select
                    multiple
                    style="width: 100%"
                    v-model="formState.sharindConditionArr"
                    clearable
                    :disabled="disabled"
                    :placeholder="disabled ? ' ' : '请选择共享要求'"
                  >
                    <el-option
                      v-for="item in shardConditionOpt"
                      :label="item.label"
                      :value="item.value"
                      :key="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="共享要求" prop="sharindCondition" v-else>
                  <el-input
                    v-model.trim="formState.sharindCondition"
                    placeholder="请输入共享要求"
                    :maxlength="60"
                    clearable
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </TitleInfo>
        <TitleInfo style="width: 100%" :animation="true" v-model="titleActiveB">
          <template #title>
            <div
              style="display: flex; align-items: flex-end"
              v-if="
                ['add', 'edit', 'change'].includes(type) &&
                formState.catalogGroup === '1'
              "
            >
              <div>信息项</div>
              <div
                style="
                  margin-left: 12px;
                  font-size: 14px;
                  color: #81848e;
                  display: flex;
                  align-items: center;
                "
              >
                点击下方+新增信息项
                <el-descriptions
                  title=""
                  :column="3"
                  :colon="false"
                  :labelStyle="{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '20px',
                    height: '20px'
                  }"
                  style="width: 240px; margin-left: 12px"
                >
                  <el-descriptions-item>
                    <template slot="label">
                      <div class="circel" style="background-color: #1b00a7" />
                    </template>
                    草稿态
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <div class="circel" style="background-color: #606266" />
                    </template>
                    发布态
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <div class="circel" style="background-color: red" />
                    </template>
                    下线态
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
            <div v-else>信息项</div>
          </template>
          <template #content>
            <div class="moduleInfo" v-if="formState.catalogGroup !== '0'">
              <div class="leftTree">
                <div
                  class="leftTree-top"
                  v-if="
                    !['publish', 'publishEdit', 'publishChange'].includes(type)
                  "
                >
                  <el-dropdown placement="top-start" @command="handleCommand">
                    <i
                      class="el-icon-circle-plus-outline"
                      style="font-size: 32px; color: #0e88eb"
                    />
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item icon="el-icon-thumb" command="a"
                        >选择模型</el-dropdown-item
                      >
                      <el-dropdown-item icon="el-icon-plus" command="b"
                        >新增模型</el-dropdown-item
                      >
                      <el-dropdown-item
                        v-if="displayIntegratedResources"
                        icon="el-icon-connection"
                        command="c"
                        >新增融合资源</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
                <div
                  style="display: flex"
                  v-if="
                    ['publish', 'publishEdit', 'publishChange'].includes(
                      type
                    ) && isShowAddBtn
                  "
                >
                  <el-button
                    size="mini"
                    type="primary"
                    @click="handleEditInfo('1')"
                    >选择数据库目录</el-button
                  >
                  <el-button
                    v-if="displayIntegratedResources"
                    size="mini"
                    type="primary"
                    @click="handleEditInfo('2')"
                  >
                    <span v-if="virtualTreeList.length > 0">编辑融合资源</span>
                    <span v-else>新增融合资源</span>
                  </el-button>
                </div>
                <div class="leftTree-bottom">
                  <el-scrollbar
                    v-loading="treeLoading"
                    ref="scrollbarRef"
                    style="height: 100%"
                  >
                    <el-tree
                      :default-expand-all="true"
                      class="treeList"
                      :props="defaultProps"
                      :data="treeList"
                      :default-checked-keys="defaultSelectedKeys"
                      node-key="id"
                      check-strictly
                      highlight-current
                      :show-checkbox="
                        ['publish', 'publishEdit', 'publishChange'].includes(
                          type
                        )
                      "
                      @node-click="handleNodeClick"
                      @check="handleNodeCheck"
                      @node-collapse="updateScrollbar"
                      @node-expand="updateScrollbar"
                      ref="inBoxTree"
                    >
                      <div class="custom-tree-node" slot-scope="{ node, data }">
                        <el-tooltip
                          class="item"
                          effect="dark"
                          :disabled="node.label.length < 6"
                          :content="node.label"
                          placement="right"
                        >
                          <div
                            style="
                              flex: 1;
                              margin-right: 4px;
                              width: 100px;
                              overflow: hidden;
                              text-overflow: ellipsis;
                              white-space: nowrap;
                            "
                            :class="{
                              deletedClass: data.isDel === '1',
                              blueColor: data.statusCode === '0'
                            }"
                          >
                            {{ node.label }}
                          </div>
                        </el-tooltip>
                        <div>
                          <i
                            v-if="
                              node.level === 1 &&
                              ['add', 'edit', 'change'].includes(type)
                            "
                            class="el-icon-edit"
                            @click.stop="handleEditModel(data)"
                          />

                          <i
                            v-if="
                              node.level === 1 &&
                              ['add', 'edit', 'change'].includes(type)
                            "
                            class="el-icon-delete"
                            @click.stop="handleDeleteTreeNode(data)"
                          />

                          <el-tooltip
                            class="item"
                            effect="dark"
                            :content="data.isDel === '1' ? '恢复' : '下线'"
                            placement="right"
                          >
                            <i
                              v-if="
                                node.level === 2 &&
                                data.statusCode === '2' &&
                                ['change', 'audit'].includes(type)
                              "
                              :class="{
                                'el-icon-circle-plus-outline':
                                  data.isDel === '1',
                                'el-icon-delete': data.isDel !== '1'
                              }"
                              @click.stop="handleChangeEntitiesIsDel(data)"
                            />
                          </el-tooltip>

                          <el-tag
                            type="success"
                            v-if="node.level === 1"
                            size="mini"
                            >模型</el-tag
                          >

                          <el-tag
                            type="info"
                            class="tag-info"
                            v-if="node.level === 2 && data.entityTypeCode"
                            size="mini"
                            >{{ data.entityType }}</el-tag
                          >
                        </div>
                      </div>
                    </el-tree>
                  </el-scrollbar>
                </div>
              </div>
              <div class="rightTable">
                <TablePlus
                  v-if="selectedType === 'table'"
                  ref="tableRef"
                  :data="formState.list"
                  v-loading="infoItemLoading"
                  border
                  fit
                  stripe
                  highlight-current-row
                  @header-dragend="handleHeaderDrag"
                >
                  <el-table-column
                    prop="itemName"
                    label="信息项名称"
                    min-width="140"
                    fixed="left"
                    show-overflow-tooltip
                  >
                  </el-table-column>
                  <el-table-column
                    v-if="formState.catalogGroup === '2'"
                    prop="itemCode"
                    label="编码"
                    width="140"
                    show-overflow-tooltip
                  >
                  </el-table-column>
                  <el-table-column
                    prop="dataTypeCode"
                    label="数据类型"
                    min-width="120"
                    align="center"
                    show-overflow-tooltip
                  >
                  </el-table-column>

                  <el-table-column
                    prop="dataLength"
                    label="数据长度"
                    width="120"
                    align="right"
                    show-overflow-tooltip
                  ></el-table-column>

                  <el-table-column
                    v-if="formState.catalogGroup === '1'"
                    prop="isRelate"
                    label="是否关联字段"
                    min-width="120"
                    align="center"
                    show-overflow-tooltip
                  >
                    <template slot-scope="{ row }">
                      <el-switch
                        v-if="['add', 'edit', 'change'].includes(type)"
                        v-model="row.isRelate"
                        @change="handleToggleSwitch('1', row)"
                      >
                      </el-switch>
                      <span v-else> {{ row.isRelate ? '是' : '否' }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column
                    v-if="formState.catalogGroup === '2' && type !== 'publish'"
                    prop="isBind"
                    label="是否注册资源"
                    min-width="120"
                    align="center"
                    show-overflow-tooltip
                  >
                    <template slot-scope="{ row }">
                      <span>
                        {{
                          row.isBind === '1'
                            ? '是'
                            : row.isBind === '-1'
                            ? '已删除'
                            : '否'
                        }}</span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="itemDesc"
                    label="信息项描述"
                    width="180"
                    show-overflow-tooltip
                  >
                  </el-table-column>
                  <!-- <el-table-column
                  v-if="formState.cataType === '3' && disabled"
                  prop="isBusinessCol"
                  label="是否业务字段"
                  min-width="120"
                  align="center"
                  show-overflow-tooltip
                >
                  <template slot-scope="{ row }">
                    {{ row.isBusinessCol ? '是' : '否' }}
                  </template>
                </el-table-column> -->

                  <!-- <el-table-column
                  v-else
                  prop="isBiz"
                  label="是否业务字段"
                  min-width="120"
                  align="center"
                  show-overflow-tooltip
                >
                  <template slot-scope="{ row }">
                    <el-switch
                      v-if="['add', 'edit'].includes(type)"
                      v-model="row.isBiz"
                      @change="handleToggleSwitch('2', row)"
                    >
                    </el-switch>
                    <span v-else> {{ row.isBiz ? '是' : '否' }}</span>
                  </template>
                </el-table-column> -->
                  <el-table-column
                    fixed="right"
                    v-if="
                      ['publish', 'publishEdit', 'publishChange'].includes(type)
                    "
                    prop="isShare"
                    label="是否共享"
                    width="120"
                    align="center"
                    show-overflow-tooltip
                  >
                    <template slot-scope="{ row, $index }">
                      <el-switch
                        v-model="row.isShare"
                        @change="handleIsShare(row, $index)"
                      >
                      </el-switch>
                    </template>
                  </el-table-column>
                </TablePlus>
                <el-descriptions
                  title=""
                  border
                  :column="1"
                  :labelStyle="{ width: '100px' }"
                  v-if="selectedType !== 'table'"
                >
                  <el-descriptions-item label="实体名称">{{
                    selectedObj.entityName
                  }}</el-descriptions-item>
                  <el-descriptions-item label="实体类型">
                    <el-tag size="mini" class="tag-info" type="info">{{
                      selectedObj.entityType
                    }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="SQL">{{
                    selectedObj.sql
                  }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间">{{
                    selectedObj.createTime
                  }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
            <div v-else class="infoItem">
              <!--列表表格区-->
              <div
                v-if="!disabled"
                style="text-align: right; margin-bottom: 10px"
              >
                <PermButton
                  label="新增"
                  type="primary"
                  icon="add"
                  size="mini"
                  style="margin-left: 12px"
                  @click="handleAdd"
                />
              </div>
              <TablePlus
                ref="tableRef"
                :data="formState.list"
                v-loading="infoItemLoading"
                border
                fit
                stripe
                highlight-current-row
                @header-dragend="handleHeaderDrag"
              >
                <el-table-column
                  prop="itemName"
                  label="信息项名称"
                  min-width="180"
                  fixed="left"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  prop="dataTypeCode"
                  label="数据类型"
                  min-width="130"
                  align="center"
                  show-overflow-tooltip
                >
                </el-table-column>

                <el-table-column
                  prop="dataLength"
                  label="数据长度"
                  min-width="130"
                  align="right"
                  show-overflow-tooltip
                ></el-table-column>

                <el-table-column
                  prop="itemDesc"
                  label="信息项描述"
                  min-width="180"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  v-if="!disabled"
                  label="操作"
                  align="center"
                  fixed="right"
                  header-align="center"
                  min-width="180"
                >
                  <template slot-scope="scope">
                    <perm-button-group :config="getInfoItemButtons(scope)" />
                  </template>
                </el-table-column>
              </TablePlus>
            </div>
          </template>
        </TitleInfo>
      </template>
    </el-form>

    <template #footerForm>
      <el-form
        v-if="
          ['publishChange', 'publishChangeAudit', 'publishChangeView'].includes(
            type
          ) && activeTab !== 'process'
        "
        ref="changeFormRef"
        :model="changeFormState"
        label-width="140px"
        size="small"
        :disabled="['publishChangeAudit', 'publishChangeView'].includes(type)"
        :rules="changeFormRules"
      >
        <TitleInfo title="变更信息" />
        <el-row type="flex" style="flex-wrap: wrap">
          <el-col :span="8">
            <el-form-item label="变更类型" prop="chgType">
              <el-select v-model="changeFormState.chgType">
                <el-option
                  v-for="(item, index) in changeReasonList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="16">
            <el-form-item label="变更说明" prop="chgDes">
              <el-input
                v-model.trim="changeFormState.chgDes"
                placeholder="请输入变更说明"
                type="textarea"
                maxlength="200"
                clearable
              />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24" v-if="type === 'publishChange'">
            <el-form-item label="是否保存原版本" prop="isSaveOriginalVersion">
              <el-checkbox-group v-model="selectedResrouces">
                <el-checkbox
                  v-for="item in publishedResourceList"
                  :label="item.id"
                  :key="item.id"
                  >{{ item.name }}</el-checkbox
                >
              </el-checkbox-group>

            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>

      <el-form
        v-if="
          [
            'audit',
            'catalogRegAudit',
            'catalogPublishAudit',
            'publishChangeAudit'
          ].includes(type) && activeTab !== 'process'
        "
        ref="confirmFormRef"
        :model="confirmFormState"
        label-width="140px"
        size="small"
        :rules="rulesToConfirm"
      >
        <TitleInfo title="审核信息" />
        <el-alert
          v-if="showOrderedWarning"
          title="变更的库表已被订阅，请核实变更内容是否会影响订阅方正常使用！"
          type="warning"
          show-icon
          :closable="false"
          style="margin-bottom: 15px"
        />
        <el-row>
          <el-col :span="24">
            <el-form-item label="审核信息" prop="handleOpinion">
              <el-input
                v-model.trim="confirmFormState.handleOpinion"
                :placeholder="'请输入审核信息'"
                type="textarea"
                maxlength="200"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>

    <template #footer>
      <el-button
        v-if="
          ![
            'view',
            'publishView',
            'changeView',
            'publishChangeView',
            'historyView'
          ].includes(type)
        "
        size="mini"
        type="primary"
        class="close-btn"
        @click="handleClose"
        :loading="btnLoading"
        >取消</el-button
      >
      <el-button
        v-else
        size="mini"
        type="primary"
        class="close-btn"
        @click="handleClose"
        :loading="btnLoading"
        >关闭</el-button
      >
      <el-button
        v-if="type === 'add' || type === 'edit'"
        size="mini"
        type="primary"
        @click="save('0')"
        :loading="btnLoading"
        >保存</el-button
      >
      <el-button
        v-if="type === 'add' || type === 'edit'"
        size="mini"
        @click="save('1')"
        :loading="btnLoading"
        >保存并自检</el-button
      >
      <el-button
        v-if="type === 'change'"
        size="mini"
        type="primary"
        @click="save('2')"
        :loading="btnLoading"
        >提交变更</el-button
      >
      <el-button
        v-if="
          [
            'audit',
            'catalogRegAudit',
            'catalogPublishAudit',
            'publishChangeAudit'
          ].includes(type) && activeTab !== 'process'
        "
        size="mini"
        :loading="btnLoading"
        type="primary"
        :disabled="submitBtnDisabled"
        class="smart_city__refuse-btn dynamic-color-btn"
        :style="{
          '--btn-color': buttonConfig.reject.color,
          backgroundColor: buttonConfig.reject.color,
          borderColor: buttonConfig.reject.color
        }"
        @click="handleConfirmDir('reject')"
        >{{ buttonConfig.reject.label }}</el-button
      >
      <el-button
        v-if="
          [
            'audit',
            'catalogRegAudit',
            'catalogPublishAudit',
            'publishChangeAudit'
          ].includes(type) && activeTab !== 'process'
        "
        size="mini"
        :loading="btnLoading"
        type="primary"
        :disabled="submitBtnDisabled"
        class="dynamic-color-btn"
        :style="{
          '--btn-color': buttonConfig.approval.color,
          backgroundColor: buttonConfig.approval.color,
          borderColor: buttonConfig.approval.color
        }"
        @click="handleConfirmDir('agree')"
        >{{ buttonConfig.approval.label }}</el-button
      >

      <!-- <el-button
        v-if="['publish'].includes(type)"
        size="mini"
        :loading="btnLoading"
        type="primary"
        @click="handleEditInfo"
        >上一步</el-button
      > -->
      <el-button
        v-if="['publish', 'publishEdit'].includes(type)"
        size="mini"
        :loading="btnLoading"
        type="primary"
        @click="handlePublish('0')"
        >保存</el-button
      >
      <el-button
        v-if="['publish', 'publishEdit'].includes(type)"
        size="mini"
        :loading="btnLoading"
        type="primary"
        @click="handlePublish('1')"
        >保存并发布</el-button
      >
      <el-button
        v-if="['publishChange'].includes(type)"
        size="mini"
        :loading="btnLoading"
        type="primary"
        @click="handlePublish('2')"
        >变更</el-button
      >
    </template>
  </TDrawer>
</template>

<script>
import {
  computed,
  defineComponent,
  getCurrentInstance,
  reactive,
  ref,
  nextTick,
  watch
} from 'vue'
import TDrawer from 'biz/components/common/t-drawer'
import TitleInfo from 'biz/components/common/t-titleInfo'
import TablePlus from '@/core/components/TablePlus'
import SelectPlus from '@/core/components/SelectPlus'
import PermButton from '@/core/components/PermButton'
import SvgIcon from '@/core/components/SvgIcon'
import PermButtonGroup from '@/core/components/PermButtonGroup'
import ProgressView from 'biz/components/business/progressView'
import {
  getInfoItemByEntityId,
  registerModleCatalog,
  queryRelateModels,
  validateModels,
  queryPhysicalModuleTableList,
  queryColByEntityIds,
  registerAndPublishModelCatalog,
  batchModifyEntityCatalog,
  queryNdrcCatalogDetail,
  changeModleCatalog,
  queryndrcCatalogChangeDetail,
  batchModifyColBiz,
  modelsPreValidate,
  queryHandlePersonId,
  editDataCatalog,
  changeDataCatalog,
  checkResBeOrder,
  checkEntityBeOrder
} from 'biz/http/api'
import { cloneDeep, throttle } from 'lodash-es'
import { checkPhoneNew } from '@/biz/utils/validate'
import Cookies from 'js-cookie'
import useSystemConfig from '../../resourceManage/components/useSystemConfig'

export default defineComponent({
  name: 'AddModuleCatalog',
  components: {
    TDrawer,
    TitleInfo,
    TablePlus,
    PermButton,
    SelectPlus,
    PermButtonGroup,
    ProgressView,
    SvgIcon
  },
  setup(props, { emit }) {
    const buttonConfig = config.buttonConfig
    const { proxy } = getCurrentInstance()
    const { displayIntegratedResources } = useSystemConfig()
    const visible = ref(false)
    const isDisabledSystem = ref(false)
    const type = ref('add')
    const infoMap = {
      add: {
        title: '数据库目录注册'
      },
      edit: {
        title: '目录编辑'
      },
      change: {
        title: '目录变更'
      },
      view: {
        title: '目录查看'
      },
      changeView: {
        title: '目录变更查看'
      },
      publishView: {
        title: '目录查看'
      },
      publish: {
        title: '目录发布',
        // interface: modelCatalogPublish
        interface: registerAndPublishModelCatalog
      },
      publishEdit: {
        title: '目录编辑'
      },
      audit: {
        title: '目录变更审核'
      },
      catalogRegAudit: {
        title: '数据库目录审核'
      },
      catalogPublishAudit: {
        title: '目录发布审核'
      },
      publishChange: {
        title: '目录变更', // 数据目录发布变更
        interface: registerAndPublishModelCatalog // todo 待修改
      },
      publishChangeView: {
        title: '目录变更查看'
      },
      publishChangeAudit: {
        title: '目录变更审核'
      },
      historyView: {
        title: '历史详情'
      }
    }

    const disabled = computed(() =>
      [
        'view',
        'changeView',
        'publishView',
        'audit',
        'catalogRegAudit',
        'catalogPublishAudit',
        'publishChangeView',
        'historyView',
        'publishChangeAudit'
      ].includes(type.value)
    )

    const submitBtnDisabled = ref(false)

    const getHandlePersonId = async (id, params) => {
      const { code, data, message } = await queryHandlePersonId(id, params)

      if (code === '200' || code === 200) {
        handlePersonId.value = data
      } else if (code === '500') {
        submitBtnDisabled.value = true
        proxy.$message.info(message)
      }
    }

    const handleOpen = async (val, row) => {
      if (row?.cataId) {
        loading.value = true
      }
      getSpaceId()
      type.value = val
      visible.value = true
      queryShardType()
      queryDataType()
      // todo 获取处理人id

      if (
        [
          'audit',
          'catalogRegAudit',
          'catalogPublishAudit',
          'publishChangeAudit'
        ].includes(type.value) &&
        !proxy.$route.query.btnType
      ) {
        await getHandlePersonId(row.cataId, {
          flowType:
            type.value === 'catalogRegAudit'
              ? '4'
              : type.value === 'catalogPublishAudit'
              ? '8'
              : '5'
        })
      }

      if (['add', 'publish'].includes(val)) {
        const currentUser = proxy.$store.state.user.currentUser
        // 如果没传row或者row里面没有resourceProviderId则用当前用户数据，否则就用传了的row.resourceProviderId
        if (!row || !row?.resourceProviderId) {
          formState.unitName = currentUser.unitName || ''
          formState.resourceProviderId = currentUser.unitId || ''
          formState.providerDeptName = currentUser.unitName || ''
          formState.providerCode = currentUser.unitCode || ''
        } else {
          formState.resourceProviderId = row.resourceProviderId
        }
      } else {
        formState.resourceProviderId = row.resourceProviderId
      }

      nextTick(async () => {
        formState.catalogGroup =
          row && row.catalogGroup ? row.catalogGroup : '1'

        await getPassStatus() // 非数据库目录新增执行
        if (row && row.cataId) {
          if (row.handlePersonId) {
            // 处理代办传入的handlePersonId
            handlePersonId.value = row.handlePersonId
          }
          await handleDetail(row)
        }

        if (disabled.value) {
          getAuditRecord(row.cataId)
        }
      })
    }

    const formState = reactive({
      catalogGroup: '', // 0:常规目录 1:数据库目录 2:数据目录
      cataId: '',
      isHavingSystem: '1',
      resourceProviderId: proxy.$store.state.user.currentUser.unitId,
      providerCode: proxy.$store.state.user.currentUser.unitCode,
      providerDeptName: proxy.$store.state.user.currentUser.unitName,
      unitName: proxy.$store.state.user.currentUser.unitName,
      directoryName: '',
      categoryName: '',
      categoryId: '',
      fromSystem: '',
      systemName: '',
      sourceItemName: '',
      labelName: '',
      isPublish: '1',
      isSubmit: '0',
      list: [],
      addItemList: [],
      editCatalogItems: [],
      delItemList: [],
      pdmList: [],
      regOptType: '',
      areaRange: '1',
      useType: '1',
      domian: '',
      cataAbstract: '',
      chargePerson: proxy.$store.state.user.currentUser.realName,
      createdPhone: proxy.$store.state.user.currentUser.mobile,
      isCascade: '0',
      shardType: undefined,
      sharindCondition: '',
      sharindConditionArr: [],
      sharindConditionArrToString: '',
      sharindMethod: undefined
    })

    const rules = {
      unitName: [{ required: true, message: '请选择部门', trigger: 'change' }],
      fromSystem: [
        { required: true, message: '请选择所属系统', trigger: 'change' }
      ],
      directoryName: [
        {
          required: true,
          trigger: 'blur',
          validator: async (rule, value, callback) => {
            if (!value) {
              callback(new Error('请输入目录名称'))
            } else {
              const res =
                await proxy.$api.bizApi.governmentInformationRegister.checkNameRepetition(
                  value,
                  formState.cataId,
                  formState.catalogGroup === '1' ? '1' : ''
                )
              if (res.code === '200' || res.code === 200) {
                callback()
              } else {
                callback(new Error('该目录名称已存在,请重新输入'))
              }
            }
          }
        }
      ],
      labelName: [
        {
          trigger: 'blur',
          validator: (rule, value, callback) => {
            const reg = /^[\u4e00-\u9fa5,a-zA-Z0-9]+$/
            if (value) {
              if (!reg.test(value)) {
                return callback(new Error('只允许输入字母，数字及英文逗号'))
              } else {
                callback()
              }
            } else {
              callback()
            }
          }
        }
      ],
      categoryName: [
        { required: true, message: '请选择目录分类', trigger: 'change' }
      ],
      cataAbstract: [
        { required: true, message: '请输入目录摘要', trigger: 'blur' }
      ],
      shardType: [
        { required: true, message: '请选择共享类型', trigger: 'change' }
      ],
      sharindMethod: [
        { required: true, message: '请选择共享方式', trigger: 'change' }
      ],
      sharindCondition: [
        { required: true, message: '请选择共享要求', trigger: 'change' }
      ],
      sharindConditionArr: [
        { required: true, message: '请选择共享要求', trigger: 'change' }
      ],
      chargePerson: [
        { required: true, message: '请输入负责人', trigger: 'blur' }
      ],
      createdPhone: [
        {
          required: true,
          message: '请输入负责人联系方式',
          trigger: 'blur'
        },
        {
          validator: checkPhoneNew,
          messageSelf:
            '请输入手机号码（11位数字）或座机（区号(0开头,3-4位数字)-座机号码(7-8位数字)',
          trigger: 'blur'
        }
      ]
    }

    const activeTab = ref('info')
    const loading = ref(false)
    const btnLoading = ref(false)
    const titleActiveA = ref(true)
    const titleActiveB = ref(true)
    const titleActiveC = ref(true)
    const handleClose = () => {
      showOrderedWarning.value = false
      submitBtnDisabled.value = false
      btnLoading.value = false
      loading.value = false
      isDisabledSystem.value = false
      isShowCatalogTypeSwitch.value = true
      selectedCatalogs.value = []
      activeTab.value = 'info'
      selectedType.value = 'table'
      titleActiveA.value = true
      titleActiveB.value = true
      titleActiveC.value = true
      showCategory.value = false
      formRef.value.resetFields()
      formState.pdmList = []
      formState.catalogGroup = '1'
      formState.categoryId = ''
      formState.list = []
      formState.addItemList = []
      formState.editCatalogItems = []
      formState.delItemList = []
      formState.systemName = ''
      formState.cataId = ''
      formState.sharindConditionArr = ''
      formState.sharindConditionArrToString = ''
      formState.isCascade = '0'
      formState.categoryName = ''
      formState.resourceCode = ''
      formState.sourceItemName = ''
      formState.shardType = ''
      formState.sharindMethod = ''
      formState.sharindCondition = ''
      formState.sharindConditionArr = ''
      formState.resourceProviderId = proxy.$store.state.user.currentUser.unitId
      formState.providerCode = proxy.$store.state.user.currentUser.unitCode
      formState.providerDeptName = proxy.$store.state.user.currentUser.unitName
      formState.unitName = proxy.$store.state.user.currentUser.unitName
      realTreeList.value = []
      virtualTreeList.value = []
      defaultSelectedKeys.value = []
      selectedFields.value = []
      confirmFormState.handleOpinion = ''
      deletedEntities.value = []
      publishedTables.value = []
      publishedCols.value = []
      virtualTable.value = ''
      isShowAddBtn.value = true
      changeFormRef.value && changeFormRef.value.resetFields()
      confirmFormRef.value && confirmFormRef.value.resetFields()
      emit('close')
      visible.value = false
    }

    const showCategory = ref(false)
    const openDirectory = (type) => {
      if (disabled.value) {
        return
      }
      let timer = new Date().getTime()
      const checkedKeys =
        type === 1
          ? timer + '$' + formState.categoryId
          : timer + '$' + formState.refCategoryIds.toString()

      emit('openRelateClassifyDrawerCallback', type, checkedKeys, formState.fromSystem)
    }

    const cleanCategory = () => {
      formState.categoryName = ''
      formState.categoryId = ''
      formState.resourceCode = ''
    }

    const fromSystemList = ref([])
    const courseList = ref([])

    // 获取来源系统下拉
    const getPassStatus = async (val) => {
      const { code, data } =
        await proxy.$api.bizApi.governmentInformationRegister.getAllSystemList({
          deptId: formState.resourceProviderId
        })
      if (code === '200' || code === 200) {
        fromSystemList.value = data || []
        courseList.value = data || []
        if (val) {
          formState.fromSystem = data?.find(
            (item) => item.systemName === val
          ).systemId
        }
      }
    }

    /** 懒加载来源系统 --------开始------------------ */
    const rangeNumber = ref(10)
    const timer = ref(null)
    const searchLoad = ref(false)
    const loadMore = (n) => {
      return () => (rangeNumber.value += 5)
    }

    // 监听select下拉框的显示和隐藏
    const visibleChange = (flag) => {
      // 显示时初始化列表
      flag && filterMethod('') // 初始化默认值
      rangeNumber.value = 10
    } // 过滤课件

    const filterMethod = (query) => {
      if (timer.value != null) clearTimeout(timer.value)
      !searchLoad.value && (searchLoad.value = true)
      timer.value = setTimeout(() => {
        fromSystemList.value = query
          ? courseList.value.filter((el) => el.systemName.includes(query))
          : courseList.value
        clearTimeout(timer.value)
        searchLoad.value = false
        rangeNumber.value = 10
        timer.value = null
      }, 500)
    }

    /** 懒加载来源系统 --------结束------------------ */

    const tableRef = ref(null)

    const handleHeaderDrag = () => {
      nextTick(() => {
        tableRef.value.doLayout()
      })
    }

    const handleCommand = (command) => {
      if (command === 'a') {
        emit('chooseModule')
      } else if (command === 'b') {
        emit('addModule', false)
      } else {
        emit('addModule', true)
      }
    }

    const realTreeList = ref([])
    const virtualTreeList = ref([])
    const treeList = computed(() => [
      ...realTreeList.value,
      ...virtualTreeList.value
    ])

    const publishedTables = ref([]) // 保存已发布的tables
    const virtualTable = ref('') // 保存已发布的虚拟表tableId
    const publishedCols = ref([]) // 保存已发布的字段
    const defaultProps = {
      children: 'children',
      label: 'name',
      id: 'id',
      disabled: (data) => {
        return data.type === 'model'
      }
    }

    const scrollbarRef = ref(null)
    const selectedType = ref('table') // 控制展示右边内容
    const selectedObj = reactive({})
    /* eslint-disable */
    const handleNodeClick = async (data, node) => {
      if (node.level === 2) {
        selectedType.value = data.entityTypeCode
        if (data.entityTypeCode === 'table') {
          queryInfoItemByEntityId(data)
        } else {
          selectedObj.entityType = data.entityType
          selectedObj.entityId = data.entityId
          selectedObj.entityName = data.entityName
          selectedObj.createTime = data.createTime
          selectedObj.sql = data.sql
        }

        if (data.resourceId && type.value === 'publishChange') {
          const res = await checkResBeOrder(data.resourceId)
          if (res.code === '200' && res.data) {
            if (data?.tableResType === '2') {
              // 选中的是虚拟表
              const tempNode = virtualTreeList.value[0].children[0]
              tempNode.isOrdered = true
              tempNode.tableColumnDtoList = tempNode.tableColumnDtoList.map(
                (item) => {
                  return {
                    ...item,
                    isOrdered: true
                  }
                }
              )
              formState.list = cloneDeep(tempNode.tableColumnDtoList)
            } else {
              // 实体表
              const modelIndex = realTreeList.value.findIndex(
                (val) => val.id === data.modelId
              )

              const tableIndex = realTreeList.value[
                modelIndex
              ].children.findIndex((item) => item.entityId === data.id)

              const tempNode =
                realTreeList.value[modelIndex].children[tableIndex]
              tempNode.isOrdered = true

              tempNode.tableColumnDtoList = tempNode.tableColumnDtoList.map(
                (item) => {
                  return {
                    ...item,
                    isOrdered: true
                  }
                }
              )

              formState.list = cloneDeep(tempNode.tableColumnDtoList)
            }
          }
        }
      }
    }

    const inBoxTree = ref(null)
    const setTreeChecked = (id) => {
      inBoxTree.value.setChecked(id, true)
    }

    // 获取第一个勾选状态的实体
    const getFirstCheckedEntity = () => {
      if (!treeList.value || !treeList.value.length) {
        return null
      }

      // 遍历所有模型和实体，找到第一个勾选状态的实体
      for (const model of treeList.value) {
        if (model.children && model.children.length) {
          for (const entity of model.children) {
            // 检查实体是否有勾选状态（通过isBind字段）
            if (entity.isBind === '1') {
              return entity // 返回第一个勾选的实体
            }
          }
        }
      }

      // 如果没有找到勾选的实体，返回第一个有实体的模型的第一个实体
      for (const model of treeList.value) {
        if (model.children && model.children.length) {
          return model.children[0]
        }
      }

      return null
    }
    const handleNodeCheck = async (data, { checkedKeys }) => {
      if (data.type === 'table') {
        await queryInfoItemByEntityId(data)
        if (!formState.list.length) {
          inBoxTree.value.setChecked(data.id, false)
        } else {
          // defaultSelectedKeys.value.push(data.id)
          // inBoxTree.value.setChecked(data.id, tr)
        }

        if (data.resourceId && type.value === 'publishChange') {
          if (checkedKeys.includes(data.id)) {
            return
          }
          const res = await checkResBeOrder(data.resourceId)
          if (res.code === '200' && res.data) {
            // proxy.$message.info('该资源已被订阅，不允许修改')
            // inBoxTree.value.setChecked(data.id, true)
            emit('openOrderDialog', {
              resourceId: data.resourceId,
              id: data.id,
              resourceName: data.name,
              entityOrFied: '1'
            })
            if (data?.tableResType === '2') {
              // 选中的是虚拟表
              const node = virtualTreeList.value[0].children[0]
              node.isOrdered = true
              node.tableColumnDtoList = node.tableColumnDtoList.map((item) => {
                return {
                  ...item,
                  isOrdered: true
                }
              })
              formState.list = cloneDeep(node.tableColumnDtoList)
            } else {
              // 实体表
              const modelIndex = realTreeList.value.findIndex(
                (val) => val.id === data.modelId
              )

              const tableIndex = realTreeList.value[
                modelIndex
              ].children.findIndex((item) => item.entityId === data.id)

              const node = realTreeList.value[modelIndex].children[tableIndex]
              node.isOrdered = true

              node.tableColumnDtoList = node.tableColumnDtoList.map((item) => {
                return {
                  ...item,
                  isOrdered: true
                }
              })

              formState.list = cloneDeep(node.tableColumnDtoList)
            }
          }
        }
      }
    }

    // 更新滚动条
    const updateScrollbar = () => {
      if (scrollbarRef.value) {
        let num = 0
        let timer = setInterval(() => {
          num += 1
          scrollbarRef.value && scrollbarRef.value.update()
          if (num === 3) {
            clearInterval(timer)
          }
        }, 300)
      }
    }

    // 重置右侧表格内容为默认第一个模型下第一个实体的内容
    const resetRightTableContent = async () => {
      try {
        // 如果左侧没有数据，清空右侧表格
        if (!treeList.value || !treeList.value.length) {
          formState.list = []
          selectedType.value = 'table'
          // 清空树的选中状态
          if (inBoxTree.value) {
            inBoxTree.value.setCurrentKey(null)
          }
          return
        }

        // 如果左侧有数据，显示第一个模型下第一个实体的内容
        if (treeList.value[0].children && treeList.value[0].children.length) {
          const firstNode = treeList.value[0].children[0]
          selectedType.value = firstNode.entityTypeCode

          // 设置第一个模型下的第一个实体节点为高亮选中状态
          if (inBoxTree.value) {
            inBoxTree.value.setCurrentKey(firstNode.id)
          }

          if (firstNode.entityTypeCode === 'table') {
            if (firstNode.tableResType === '2') {
              formState.list = cloneDeep(
                virtualTreeList.value[0].children[0]?.tableColumnDtoList || []
              )
            } else {
              // 查询实体表的字段信息
              await queryInfoItemByEntityId(firstNode)
            }
          } else {
            selectedObj.entityType = firstNode.entityType
            selectedObj.entityId = firstNode.entityId
            selectedObj.entityName = firstNode.entityName
            selectedObj.createTime = firstNode.createTime
            selectedObj.sql = firstNode.sql
          }
        } else {
          // 如果第一个模型下没有实体，清空右侧表格
          formState.list = []
          selectedType.value = 'table'
          if (inBoxTree.value) {
            inBoxTree.value.setCurrentKey(null)
          }
        }
      } catch (e) {
        console.log('resetRightTableContent error', e)
        formState.list = []
      }
    }

    // 取消关联模型
    const handleDeleteTreeNode = (data) => {
      proxy
        .$confirm('您确认要取消选择当前模型吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
        .then(async () => {
          const index = realTreeList.value.findIndex(
            (item) => item.id === data.id
          )
          if (index !== -1) {
            realTreeList.value.splice(index, 1)
            deletedEntities.value = deletedEntities.value.filter((item) =>
              realTreeList.value.some((i) => i.id === item.modelId)
            )

            // 重置右侧表格内容
            await resetRightTableContent()
          }
        })
    }

    const handleChangeEntitiesIsDel = (val) => {
      if (val.isBeOrder === '1') {
        proxy.$message.info('该资源已被订阅，不允许下线')
        return
      }
      const index = deletedEntities.value.findIndex(
        (item) => item.entityId === val.id
      )
      if (val.isDel === '1' && index !== -1) {
        // 如果已设置为上线(isDel='1')且在删除列表中找到了该项，则将其从删除列表中移除
        deletedEntities.value.splice(index, 1)
        // 上线操作立即更新节点状态
        const node = inBoxTree.value.getNode(val.id)
        node.data.isDel = '0'
      } else if (val.isDel === '0') {
        // 如果设置为下线(isDel='0')
        proxy
          .$confirm(
            '表下线将会删除表结构及数据，请确认是否需要下线？',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消'
            }
          )
          .then(() => {
            // 如果确认下线且尚未在删除列表中，则添加到删除列表
            if (index === -1) {
              deletedEntities.value.push({
                entityId: val.id,
                modelId: val.modelId,
                innerCode: val.innerCode
              })
            }
            // 用户确认后才更新节点状态为下线
            const node = inBoxTree.value.getNode(val.id)
            node.data.isDel = '1'
          })
      }
    }

    // 编辑模型
    const handleEditModel = (row) => {
      const index = realTreeList.value.findIndex((el) => el.id === row.id)
      if (index !== -1) {
        realTreeList.value[index].modelIsEdit = '1'
      }
      emit('editModel', row)
    }

    const infoItemLoading = ref(false)
    // 查询模型表信息项
    const queryInfoItemByEntityId = async (row) => {
      try {
        if (row.tableResType === '2') {
          formState.list =
            virtualTreeList.value[0].children[0].tableColumnDtoList
          return
        }

        infoItemLoading.value = true
        const { code, data } = await getInfoItemByEntityId({
          entityId: row.id
        })
        if (code === '200' && data && data.length) {
          const modelIndex = realTreeList.value.findIndex(
            (val) => val.id === row.modelId
          )

          const tableIndex = realTreeList.value[modelIndex].children.findIndex(
            (item) => item.innerCode === row.innerCode
          )

          const isRequest =
            realTreeList.value[modelIndex].children[tableIndex].isRequest

          if (!isRequest) {
            /* eslint-disable */

            formState.list = data.map((item) => {
              return {
                ...item,
                isShare: true,
                dataType: item.dataTypeCode,
                dataTypeCode: item.dataType,
                innerCode: row.innerCode,
                modelId: row.modelId,
                entityId: row.id,
                isEdit: false,
                relateFiedIsOnlyRead:
                  realTreeList.value[modelIndex].children[tableIndex]
                    .relateFiedIsOnlyRead,
                isRelate: item.isRelate === '1',
                isBiz: item.isBiz === '1'
              }
            })
            realTreeList.value[modelIndex].children[
              tableIndex
            ].tableColumnDtoList = formState.list
            realTreeList.value[modelIndex].children[tableIndex].isRequest = true
          } else {
            formState.list =
              realTreeList.value[modelIndex].children[
                tableIndex
              ].tableColumnDtoList
          }
        } else {
          formState.list = []
        }
        infoItemLoading.value = false
      } catch (e) {
        console.log('e', e)
        formState.list = []
        infoItemLoading.value = false
      }
    }

    const formRef = ref(null)
    // 保存or保存送审
    const save = throttle(
      async (val) => {
        try {
          btnLoading.value = true
          loading.value = true
          await formRef.value.validate()
          if (val !== '0' && !treeList.value.length) {
            proxy.$message.info('请先选择信息项')
            btnLoading.value = false
            loading.value = false
            return
          }

          if (val === '0') {
            // 当val为'0'时，跳过校验直接保存
            saveFunc(val)
            handleChangeTableType()
            handleSubmitChangedFileds()
          } else {
            // 当val不为'0'时，执行校验逻辑
            const { data } = await modelsPreValidate({
              modelIds: treeList.value.map((item) => item.id).toString()
            })

            if (Object.values(data).some((item) => item.invalidCnt > 0)) {
              btnLoading.value = false
              loading.value = false
              emit(
                'openModelErrorDrawer',
                Object.keys(data)
                  .map((item) => {
                    return {
                      id: item,
                      name: treeList.value.find((i) => i.id === item).name,
                      invalidCnt: data[item].invalidCnt,
                      entityDtoList: data[item].entities
                        ?.filter((i) => i.code === '1')
                        ?.map((i) => {
                          return {
                            ...i,
                            modelId: item
                          }
                        })
                    }
                  })
                  .filter((item) => item.invalidCnt > 0)
              )

              return
            }

            const res = await validateModels({
              cataId: formState.cataId,
              pdmList: treeList.value.map((item) => {
                return {
                  modelId: item.id,
                  modelName: item.name
                }
              })
            })
            if (res.code === '200') {
              saveFunc(val)
              handleChangeTableType()
              handleSubmitChangedFileds()
            } else {
              proxy
                .$confirm(res.message, '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消'
                })
                .then(async () => {
                  saveFunc(val)
                  handleChangeTableType()
                  handleSubmitChangedFileds()
                })
                .catch(() => {
                  btnLoading.value = false
                  loading.value = false
                })
            }
          }
        } catch (e) {
          btnLoading.value = false
          loading.value = false
        }
      },
      1000,
      {
        leading: true,
        trailing: false
      }
    )

    const deletedEntities = ref([])

    const saveFunc = async (val) => {
      try {
        formState.isSubmit = val
        if (type.value === 'add') {
          formState.regOptType = 'add'
        } else {
          formState.regOptType = 'edit'
        }
        formState.pdmList = treeList.value.map((item) => {
          return {
            modelId: item.id,
            modelName: item.name
          }
        })
        const params = Object.assign({}, formState)
        params.catalogGroup = '1'
        if (params.shardType === '2') {
          params.sharindCondition = params.sharindConditionArr.toString()
        }

        if (type.value === 'change' && val === '2') {
          params.modelEntityRemoveList = deletedEntities.value
        }

        // const modelIsEdit = treeList.value.some(
        //   (item) => item.modelIsEdit === '1'
        // )

        // const flag =
        //   treeList.value.map((item) => item.id).toString() ===
        //   orginalData.value.map((item) => item.modelId).toString()
        if (val === '0') {
          params.isModelPrevTest = '0'
        } else {
          params.isModelChange = '1'
          params.isModelPrevTest = '1'
        }

        const { code } =
          val === '2'
            ? await changeModleCatalog(params)
            : await registerModleCatalog(params)
        if (code === '200') {
          proxy.$message.success(
            val === '0' ? '保存成功' : config.databaseCatalogSubmitCallbackInfo
          )
          handleClose()
          emit('addCallback')
        }
        btnLoading.value = false
        loading.value = false
      } catch (e) {
        btnLoading.value = false
        loading.value = false
      }
    }

    // val: 1为实体表，2为虚拟表
    const formatTreeData = async (val, data) => {
      try {
        if (val === '1') {
          realTreeList.value =
            data.pdmData.modelList
              ?.map((item) => {
                return {
                  ...item,
                  id: item.modelId,
                  name: item.modelName,
                  dsId: item.dsId,
                  type: 'model'
                }
              })
              ?.filter((item) => item.isBind === '1') || []

          realTreeList.value.forEach((item) => {
            if (item.entityDtoList && item.entityDtoList.length) {
              item.children =
                item.entityDtoList?.map((val) => {
                  if (val.isBind === '1') {
                    defaultSelectedKeys.value.push(val.entityId)
                  }
                  return {
                    ...val,
                    id: val.entityId,
                    name: val.entityName,
                    modelId: item.modelId,
                    dsId: item.dsId,
                    type: 'table',
                    isDel: '0',
                    isRequest: true,
                    tableColumnDtoList: val.tableInfoList?.map((i) => {
                      return {
                        ...i,
                        isEdit: false,
                        dataType: i.dataTypeCode,
                        dataTypeCode: i.dataType,
                        innerCode: val.innerCode,
                        modelId: item.modelId,
                        isRelate: i.isRelate === '1',
                        isBiz: i.isBiz === '1',
                        isShare: ['publish'].includes(type.value)
                          ? true
                          : i.isShare === '1',
                        entityId: val.entityId,
                        ressourceName: val.entityName,
                        resourceId: val.resourceId
                      }
                    })
                  }
                }) || []
            }
          })
        } else {
          virtualTreeList.value = [
            {
              id: new Date().getTime() + 'virtual',
              name: data.virtualPdmData.modelName,
              type: 'model'
            }
          ]
          virtualTreeList.value.forEach((item) => {
            if (
              data.virtualPdmData.tableList &&
              data.virtualPdmData.tableList.length
            ) {
              item.children = data.virtualPdmData.tableList?.map((val) => {
                defaultSelectedKeys.value.push(val.tableId)
                return {
                  ...val,
                  id: val.tableId,
                  name: val.tableNameCn,
                  type: 'table',
                  enetityCode: val.tableName,
                  entityType: '虚拟表',
                  entityTypeCode: 'table',
                  isRequest: true,
                  tableColumnDtoList: val.tableColumnDtoList?.map((item) => {
                    return {
                      ...item,
                      itemName: item.fieldName,
                      itemCode: item.fieldCode,
                      dataLength: item.fieldLength,
                      dataType: item.fieldType,
                      dataTypeCode: item.fieldType
                        ? dataTypeDict.value.find(
                            (val) => val.value === item.fieldType
                          ).label
                        : '',
                      isBusinessCol: item.isBusinessCol === '1',
                      isRelCol: item.isRelCol === '1',
                      isRelate: item.isRelCol === '1',
                      isBiz: item.isBusinessCol === '1',
                      isShare: item.isShare === '1',
                      tableId: val.tableId,
                      originEntityId: '',
                      originModelId: '',
                      entityId: '',
                      resourceId: val.resourceId,
                      resourceName: val.tableNameCn
                    }
                  })
                }
              })
            }
          })

          publishedCols.value = cloneDeep(
            virtualTreeList.value &&
              virtualTreeList.value.length &&
              virtualTreeList.value[0].children &&
              virtualTreeList.value[0].children.length
              ? virtualTreeList.value[0].children[0].tableColumnDtoList
              : []
          )

          if (
            publishedCols.value.length &&
            ['publishChange', 'publishEdit'].includes(type.value)
          ) {
            await innerCodeToEntityId()
          }
        }

        setTimeout(async () => {
          // 默认展示第一个勾选状态的实体的字段信息
          const firstNode = getFirstCheckedEntity()

          if (firstNode) {
            selectedType.value = firstNode.entityTypeCode

            // 设置第一个勾选状态的实体节点为高亮选中状态
            if (inBoxTree.value) {
              inBoxTree.value.setCurrentKey(firstNode.id)
            }

            if (firstNode.entityTypeCode === 'table') {
              if (firstNode.tableResType === '2') {
                formState.list = cloneDeep(
                  virtualTreeList.value[0].children[0]?.tableColumnDtoList
                )
              } else {
                formState.list = cloneDeep(firstNode?.tableColumnDtoList)
              }

              // 初始化字段是否被订阅
              if (firstNode.resourceId && type.value === 'publishChange') {
                const res = await checkResBeOrder(firstNode.resourceId)
                if (res.code === '200' && res.data) {
                  if (firstNode?.tableResType === '2') {
                    // 选中的是虚拟表
                    const node = virtualTreeList.value[0].children[0]
                    node.tableColumnDtoList = node.tableColumnDtoList.map(
                      (item) => {
                        return {
                          ...item,
                          isOrdered: true
                        }
                      }
                    )
                    formState.list = cloneDeep(node.tableColumnDtoList)
                  } else {
                    // 实体表
                    const modelIndex = realTreeList.value.findIndex(
                      (val) => val.id === firstNode.modelId
                    )

                    const tableIndex = realTreeList.value[
                      modelIndex
                    ].children.findIndex(
                      (item) => item.entityId === firstNode.id
                    )

                    const node =
                      realTreeList.value[modelIndex].children[tableIndex]

                    node.tableColumnDtoList = node.tableColumnDtoList.map(
                      (item) => {
                        return {
                          ...item,
                          isOrdered: true
                        }
                      }
                    )

                    formState.list = cloneDeep(node.tableColumnDtoList)
                  }
                }
              }
            } else {
              selectedObj.entityType = firstNode.entityType
              selectedObj.entityId = firstNode.entityId
              selectedObj.entityName = firstNode.entityName
              selectedObj.createTime = firstNode.createTime
              selectedObj.sql = firstNode.sql
            }
          }
        }, 100)
      } catch (e) {
        console.log('formatTreeData error', e)
      }
    }

    // 通过详情返回字段的innerCode查询出建模系统中表的entityId添加到字段中
    const innerCodeToEntityId = async () => {
      try {
        const ids = Array.from(
          new Set(
            virtualTreeList.value[0].children[0].tableColumnDtoList.map(
              (item) => item.modelId
            )
          )
        )
        const { code, data } = await queryPhysicalModuleTableList({
          entityType: 'table',
          keepPrev: '2', // 0 获取最新版本，1 获取发布和最新版本， 2 获取发布版本
          modelIds: ids
        })

        if (code === '200') {
          virtualTreeList.value[0].children[0].tableColumnDtoList.forEach(
            (item) => {
              const modelId = item.modelId
              const innerCode = item.innerCode

              item.entityId = data
                .find((i) => i.modelId === modelId)
                .entityDtoList.find((i) => i.innerCode === innerCode).entityId
              item.originModelId = item.modelId
            }
          )
        }

        getAllMetaResour()
      } catch (e) {
        console.log('innerCodeToEntityId error', e)
      }
    }

    // 处理详情返回的虚拟表格式 给格式化后的字段添加来源模型和表
    const getAllMetaResour = async () => {
      try {
        const { code, data } = await queryColByEntityIds([
          ...new Set(
            virtualTreeList.value[0].children[0].tableColumnDtoList.map(
              (i) => i.entityId
            )
          )
        ])

        if (code == '200') {
          virtualTreeList.value[0].children[0].tableColumnDtoList =
            virtualTreeList.value[0].children[0].tableColumnDtoList
              .filter(
                (item) =>
                  data
                    .find((i) => i.entityId === item.entityId)
                    .items.findIndex((i) => i.itemCode === item.itemCode) !== -1
              )
              .map((item) => {
                const col = data
                  .find((i) => i.entityId === item.entityId)
                  .items.find((i) => i.itemCode === item.itemCode)

                return {
                  ...item,
                  ...col,
                  dataType: col.dataTypeCode,
                  dataTypeCode: col.dataType,
                  originEntityId: item.entityId,
                  modelId: virtualTreeList.value[0].id,
                  entityId: item.tableId,
                  tableId: item.tableId
                }
              })
        }
      } catch (e) {
        console.log('getAllMetaResour error', e)
      }
    }

    // 获取详情
    const handleDetail = async (row) => {
      try {
        loading.value = true
        const { code, data } =
          formState.catalogGroup === '0' &&
          ['publishChangeAudit', 'publishChangeView', 'historyView'].includes(
            type.value
          )
            ? await proxy.$api.bizApi.governmentInformationRegister.getHisListDetail(
                {
                  versionType: '0',
                  hisCataId: row.cataHisId
                }
              )
            : formState.catalogGroup === '0' &&
              ![
                'publishChangeAudit',
                'publishChangeView',
                'historyView'
              ].includes(type.value)
            ? await proxy.$api.bizApi.governmentInformationRegister.getDetailById(
                row.cataId
              )
            : [
                // 'change',
                'audit',
                'changeView',
                'publishChangeAudit',
                'publishChangeView',
                'historyView'
              ].includes(type.value) && row.cataHisId
            ? await queryndrcCatalogChangeDetail(row.cataHisId)
            : await queryNdrcCatalogDetail(
                row.cataId,
                [
                  'publish',
                  'publishEdit',
                  'publishChange',
                  'catalogPublishAudit',
                  'publishChangeAudit',
                  'publishChangeView',
                  'publishView'
                ].includes(type.value)
                  ? 'pub'
                  : ''
              )

        if (code === '200') {
          formState.cataId = data.catalogBasicInfo.cataId
          formState.directoryName = data.catalogBasicInfo.directoryName
          formState.areaRange = data.catalogBasicInfo.areaRange
          formState.useType = data.catalogBasicInfo.useType
          formState.domian = data.catalogBasicInfo.domian
          formState.cataAbstract = data.catalogBasicInfo.cataAbstract
          formState.categoryId = data?.catalogTypeInfo?.id
          formState.fromSystem = data.publicSystem.systemId
          formState.systemName = data.publicSystem.systemName
          formState.isPublish = data.catalogBasicInfo.isPublish
          formState.isCascade = data.catalogBasicInfo.isCascade
          // formState.cataHisId = row.cataHisId // 变更待送审需要传
          formState.unitName = data.catalogBasicInfo.providerDeptName
          formState.providerDeptName = data.catalogBasicInfo.providerDeptName
          formState.providerCode = data.catalogBasicInfo.providerCode
          formState.chargePerson = data.catalogBasicInfo.chargePerson
          formState.createdPhone = data.catalogBasicInfo.createdPhone
          formState.resourceProviderId =
            data.catalogBasicInfo.resourceProviderId
          formState.categoryName = data?.catalogTypeInfo?.name
          formState.resourceCode = data.catalogBasicInfo.resourceCode

          if (
            type.value !== 'publish' &&
            data.catalogBasicInfo.resourceProviderId !==
              proxy.$store.state.user.currentUser.unitId
          ) {
            isDisabledSystem.value = true
          }

          if (formState.isCascade === '1') {
            formState.sourceItemName = data.catalogBasicInfo.sourceItemName
            formState.shardType = data.catalogBasicInfo.shardType
            formState.sharindMethod = data.catalogBasicInfo.sharindMethod
            formState.sharindCondition = data.catalogBasicInfo.sharindCondition
            if (formState.shardType === '2') {
              formState.sharindConditionArr =
                data.catalogBasicInfo.sharindCondition?.split(',') || []
              formState.sharindConditionArrToString =
                data.catalogBasicInfo.sharindConditionCn
            }
          }

          let currentIndex = courseList.value.findIndex(
            (item) => item.systemId === formState.fromSystem
          )
          if (currentIndex !== -1 && currentIndex !== 0) {
            let temp = fromSystemList.value.splice(currentIndex, 1)[0] // 先删除元素并保存
            fromSystemList.value.splice(0, 0, temp) // 在指定位置插入元素
          }

          if (formState.catalogGroup === '0') {
            formState.list = data.catalogItemList?.map((item) => {
              return {
                id: item.itemId,
                accuracy: item.accuracy,
                dataType: item.dataTypeCode,
                dataTypeCode: item.dataType,
                itemName: item.itemName,
                itemDesc: item.itemDesc,
                dataLength: item.dataLength,
                isPublic: item.isPublicCode,
                isPublicCode: item.isPublic,
                sensitivityLevel: item.sensitivityLevelCode,
                sensitivityLevelCode: item.sensitivityLevel,
                lastUpdatedBy: item.lastUpdatedBy,
                lastUpdatedTime: item.lastUpdatedTime,
                createdTime: item.createdTime,
                timeType: item.timeTypeCode,
                timeTypeCode: item.timeType,
                sort: item.sort
              }
            })
          }

          if (formState.catalogGroup === '1') {
            // 数据库目录
            formatTreeData('1', data)

            // 处理数据库目录下线的模型
            deletedEntities.value = data.modelEntityRemoveList || []
            deletedEntities.value.forEach((item) => {
              const index = realTreeList.value.findIndex(
                (el) => el.id === item.modelId
              )
              if (index !== -1) {
                const tableIndex = realTreeList.value[index].children.findIndex(
                  (i) => i.id === item.entityId
                )
                if (tableIndex !== -1) {
                  realTreeList.value[index].children[tableIndex].isDel = '1'
                }
              }
            })
          } else if (formState.catalogGroup === '2') {
            // 数据目录
            if (data.pdmData.modelList && data.pdmData.modelList.length) {
              await formatTreeData('1', data)
              publishedTables.value = data.pdmData.modelList?.reduce(
                (acc, cur) => {
                  return acc.concat(cur.entityDtoList.filter((i) => i.tableId))
                },
                []
              )
            }
            if (data.virtualPdmData && data.virtualPdmData.modelName) {
              await formatTreeData('2', data)
              virtualTable.value =
                data.virtualPdmData.tableList &&
                data.virtualPdmData?.tableList.length
                  ? data.virtualPdmData?.tableList[0].tableId
                  : ''
            }
          }

          selectedCatalogs.value =
            data.catalogBasicInfo.relateCataId?.split(',')

          if (!isShowCatalogTypeSwitch.value) {
            selectedCatalogs.value = [data.catalogBasicInfo.cataId]
            formState.cataId = '' // 如果是快速注册数据库目录，则不传cataId
            formState.directoryName = ''
          }

          if (
            ['publishChangeAudit', 'publishChangeView'].includes(type.value)
          ) {
            changeFormState.chgType = data.tcataChangeRecord.chgType
            changeFormState.chgDes = data.tcataChangeRecord.chgDes
          }
        }

        if (type.value === 'audit' && row.cataHisId) {
          // 审核页面检查表是否被订阅
          const orderResult = await checkEntityBeOrder(row.cataHisId)
          if (orderResult.code === '200' && orderResult.data) {
            showOrderedWarning.value = true
          }
        }

        loading.value = false
      } catch (e) {
        console.log('e', e)
        loading.value = false
      }
    }

    // 根据已关联的数据库目录查询模型及表
    const selectedCatalogs = ref([])
    const handleRelateModels = async (ids, entityTypes = '') => {
      try {
        selectedCatalogs.value = ids
        treeLoading.value = true
        const { code, data } = await queryRelateModels(
          ids
            ? {
                catalogIds: ids
              }
            : {
                catalogId: formState.cataId
              }
        )
        if (code === '200') {
          // 去重modelId重复的
          realTreeList.value = data
            ?.map((item) => {
              return {
                name: item.modelName,
                id: item.modelId,
                children: [],
                type: 'model'
              }
            })
            .filter((item, index, arr) => {
              return arr.findIndex((i) => i.id === item.id) === index
            })

          // 获取模型下的所有表
          const res = await queryPhysicalModuleTableList({
            entityType: entityTypes,
            keepPrev: '2', // 0 获取最新版本，1 获取发布和最新版本， 2 获取发布版本
            modelIds: realTreeList.value.map((item) => item.id)
          })

          res.data.forEach((item) => {
            const index = realTreeList.value.findIndex(
              (el) => el.id === item.modelId
            )
            if (index !== -1) {
              realTreeList.value[index].dsId = item.dsId
              realTreeList.value[index].children = item.entityDtoList.map(
                (val) => {
                  return {
                    ...val,
                    id: val.entityId,
                    name: val.entityName,
                    dsId: item.dsId,
                    children: [],
                    type: 'table',
                    entityCatalogCode:
                      val.entityCatalogCode !== '1' ? '0' : '1',
                    isRequest: false,
                    isShare: true,
                    modelId: item.modelId,
                    tableId:
                      publishedTables.value.find(
                        (i) => i.innerCode === val.innerCode
                      )?.tableId || ''
                  }
                }
              )
            }
          })

          updateScrollbar()
          treeLoading.value = false
        }
      } catch (e) {
        console.log('e', e)
        treeLoading.value = false
      }
    }

    // 信息系统弹窗
    const systemAdd = () => {
      emit('openAddSystemDrawer')
    }

    const confirmFormRef = ref(null)
    const confirmFormState = reactive({
      handleOpinion: ''
    })
    const showOrderedWarning = ref(false)
    const rulesToConfirm = reactive({
      handleOpinion: [
        { required: true, message: '请输入回复意见', trigger: 'blur' }
      ]
    })

    // 审批
    const auditInterfaceMap = {
      catalogRegAudit: 'registerAudit',
      audit: 'maintainAudit',
      catalogPublishAudit: 'modelCatlaogPublishAudit',
      publishChangeAudit: 'publishChangeAudit'
    }
    const handlePersonId = ref('')
    const handleConfirmDir = async (isApprove) => {
      try {
        if (isApprove === 'reject') {
          await confirmFormRef.value.validate()
        } else {
          confirmFormRef.value.clearValidate()
        }
        loading.value = true
        btnLoading.value = true
        let params = {}
        params.handlePersonId = handlePersonId.value
        const { code, message } =
          // eslint-disable-next-line
          await proxy.$api.bizApi.auditing[auditInterfaceMap[type.value]]({
            handleOpinion: confirmFormState.handleOpinion || '同意',
            auditType: isApprove,
            ...params
          })
        btnLoading.value = false
        if (code === '200') {
          handleClose()
          emit('addCallback')
          proxy.$message.success('操作成功')
        } else {
          proxy.$message({ message: message, type: 'error' })
        }
        loading.value = false
      } catch (e) {
        console.log('e', e)
        loading.value = false
        btnLoading.value = false
      }
    }

    const defaultSelectedKeys = ref([])
    const treeLoading = ref(false)
    // 查询模型表
    const handleModuleTable = async (modelIds, flag = true) => {
      try {
        /* eslint-disable */
        treeLoading.value = true
        const { code, data } = await queryPhysicalModuleTableList({
          modelIds,
          entityType: '',
          keepPrev: '0'
        })
        deletedEntities.value = deletedEntities.value.filter((item) =>
          realTreeList.value.some((i) => i.id === item.modelId)
        )
        if (code === '200' || code === 200) {
          const tempTreeList = data.map((item) => {
            return {
              index: realTreeList.value.findIndex(
                (el) => el.id === item.modelId
              ),
              ...item,
              id: item.modelId,
              name: item.modelName,
              dsId: item.dsId,
              type: 'model',
              children: item.entityDtoList.map((val) => {
                return {
                  ...val,
                  id: val.entityId,
                  name: val.entityName,
                  dsId: item.dsId,
                  children: [],
                  type: 'table',
                  isDel: deletedEntities.value.some(
                    (item) => item.entityId === val.entityId
                  )
                    ? '1'
                    : '0',
                  entityCatalogCode: val.entityCatalogCode !== '1' ? '0' : '1',
                  isRequest: false,
                  isShare: true,
                  modelId: item.modelId,
                  relateFiedIsOnlyRead: flag
                }
              })
            }
          })

          realTreeList.value = tempTreeList.sort((a, b) => a.index - b.index)
        }

        if (treeList.value.length && treeList.value[0].children.length) {
          const node = treeList.value[0].children[0]
          selectedType.value = node.entityTypeCode

          // 设置第一个模型下的第一个实体节点为高亮选中状态
          if (inBoxTree.value) {
            inBoxTree.value.setCurrentKey(node.id)
          }

          if (node.entityTypeCode === 'table') {
            queryInfoItemByEntityId(node)
          } else {
            selectedObj.entityType = node.entityType
            selectedObj.entityId = node.entityId
            selectedObj.entityName = node.entityName
            selectedObj.createTime = node.createTime
            selectedObj.sql = node.sql
          }
        }

        treeLoading.value = false
      } catch (e) {
        treeLoading.value = false
        console.log('e', e)
      }
    }

    // 更新比模型下的所有表信息
    const updateTreeList = async () => {
      // 获取模型下的所有表
      const res = await queryPhysicalModuleTableList({
        entityType: '',
        keepPrev: '0',
        modelIds: realTreeList.value.map((item) => item.id)
      })

      deletedEntities.value = deletedEntities.value.filter((item) =>
        realTreeList.value.some((i) => i.id === item.modelId)
      )

      const tempTreeList = res.data.map((item) => {
        return {
          index: realTreeList.value.findIndex((el) => el.id === item.modelId),
          ...item,
          id: item.modelId,
          name: item.modelName,
          dsId: item.dsId,
          type: 'model',
          children: item.entityDtoList.map((val) => {
            return {
              ...val,
              id: val.entityId,
              name: val.entityName,
              dsId: item.dsId,
              children: [],
              type: 'table',
              isDel: deletedEntities.value.some(
                (item) => item.entityId === val.entityId
              )
                ? '1'
                : '0',
              entityCatalogCode: val.entityCatalogCode !== '1' ? '0' : '1',
              isRequest: false,
              isShare: true,
              modelId: item.modelId
            }
          })
        }
      })

      realTreeList.value = tempTreeList.sort((a, b) => a.index - b.index)

      if (treeList.value.length && treeList.value[0].children.length) {
        const node = treeList.value[0].children[0]
        selectedType.value = node.entityTypeCode

        // 设置第一个模型下的第一个实体节点为高亮选中状态
        if (inBoxTree.value) {
          inBoxTree.value.setCurrentKey(node.id)
        }

        if (node.entityTypeCode === 'table') {
          queryInfoItemByEntityId(node)
        } else {
          selectedObj.entityType = node.entityType
          selectedObj.entityId = node.entityId
          selectedObj.entityName = node.entityName
          selectedObj.createTime = node.createTime
          selectedObj.sql = node.sql
        }
      }

      updateScrollbar()
    }

    // 改变是否共享同步数据到左侧树
    const selectedFields = ref([])
    const handleIsShare = (row, index) => {
      const modelIndex = treeList.value.findIndex(
        (val) => val.id === formState.list[0].modelId
      )
      const tableIndex = treeList.value[modelIndex].children.findIndex(
        (item) => item.id === formState.list[0].entityId
      )

      if (
        treeList.value[modelIndex].children[tableIndex]?.tableResType === '2'
      ) {
        // 如果是选中则同步数据到左侧树
        if (row.isShare) {
          nextTick(() => {
            virtualTreeList.value[0].children[0].tableColumnDtoList = cloneDeep(
              formState.list
            )
          })
          return
        }
        // 如果是取消选择 校验是否被订阅
        if (row.isOrdered) {
          emit('openOrderDialog', {
            resourceId: row.resourceId,
            id: row.colId,
            resourceName: row.resourceName,
            entityOrFied: '2',
            modelId: formState.list[0].modelId,
            entityId: formState.list[0].entityId,
            index,
            tableResType: '2'
          })

          // proxy.$message.info('该字段已被订阅，不允许修改')
          // row.isShare =
          //   virtualTreeList.value[0].children[0].tableColumnDtoList[
          //     index
          //   ].isShare
        }
      } else {
        const modelIndexInRealTree = realTreeList.value.findIndex(
          (val) => val.id === formState.list[0].modelId
        )
        const tableIndexInRealTree = realTreeList.value[
          modelIndexInRealTree
        ].children.findIndex((item) => item.id === formState.list[0].entityId)

        if (row.isShare) {
          nextTick(() => {
            realTreeList.value[modelIndexInRealTree].children[
              tableIndexInRealTree
            ].tableColumnDtoList = cloneDeep(formState.list)
          })
          return
        }
        if (row.isOrdered) {
          emit('openOrderDialog', {
            resourceId: row.resourceId,
            id: row.colId,
            resourceName: row.resourceName,
            entityOrFied: '2',
            modelId: formState.list[0].modelId,
            entityId: formState.list[0].entityId,
            index,
            tableResType: '1'
          })

          // proxy.$message.info('该字段已被订阅，不允许修改')

          // row.isShare =
          //   realTreeList.value[modelIndexInRealTree].children[
          //     tableIndexInRealTree
          //   ].tableColumnDtoList[index].isShare
        }
      }
    }

    const setRowIsShare = ({
      index,
      entityId,
      modelId,
      tableResType,
      flag
    }) => {
      if (tableResType === '1') {
        // 如果是实体表
        if (flag) {
          // flag: true 直接同步数据到左侧树
          nextTick(() => {
            realTreeList.value
              .find((val) => val.id === modelId)
              .children.find(
                (item) => item.id === entityId
              ).tableColumnDtoList = cloneDeep(formState.list)
          })
        } else {
          formState.list[index].isShare = realTreeList.value
            .find((val) => val.id === modelId)
            .children.find((item) => item.id === entityId).tableColumnDtoList[
            index
          ].isShare

          nextTick(() => {
            realTreeList.value
              .find((val) => val.id === modelId)
              .children.find(
                (item) => item.id === entityId
              ).tableColumnDtoList = cloneDeep(formState.list)
          })
        }
      } else {
        // 如果是虚拟表
        if (flag) {
          nextTick(() => {
            virtualTreeList.value[0].children[0].tableColumnDtoList = cloneDeep(
              formState.list
            )
          })
        } else {
          formState.list[index].isShare =
            virtualTreeList.value[0].children[0].tableColumnDtoList[
              index
            ].isShare
          nextTick(() => {
            virtualTreeList.value[0].children[0].tableColumnDtoList = cloneDeep(
              formState.list
            )
          })
        }
      }
    }

    const handleToggleSwitch = (val, row) => {
      // 同步数据值左侧树
      const modelIndex = realTreeList.value.findIndex(
        (val) => val.id === formState.list[0].modelId
      )
      const tableIndex = realTreeList.value[modelIndex].children.findIndex(
        (item) => item.innerCode === formState.list[0].innerCode
      )
      if (
        treeList.value[modelIndex].children[tableIndex].tableResType === '2'
      ) {
        treeList.value[0].children[0].tableColumnDtoList = formState.list
      } else {
        const index = formState.list.findIndex(
          (item) => item.itemId === row.itemId
        )
        if (index !== -1) {
          if (val === '1') {
            formState.list[index].isRelate = row.isRelate
          } else {
            formState.list[index].isBiz = row.isBiz
          }
          formState.list[index].isEdit = true
        }

        realTreeList.value[modelIndex].children[tableIndex].tableColumnDtoList =
          formState.list

        const allTables = realTreeList.value
          .map((item) => item.children)
          .reduce((pre, cur) => {
            return pre.concat(cur)
          }, [])
          .filter((i) => i.isRequest)
        const changedFields = allTables
          .map((item) => item.tableColumnDtoList)
          .reduce((pre, cur) => {
            return pre.concat(cur)
          }, [])
          .filter((item) => item.isEdit)

        selectedFields.value = changedFields
      }
    }

    // 发布
    const handlePublish = throttle(
      async (val, isNeedOpenResourceList = true) => {
        try {
          // 如果是变更目录的变更类型是撤销发布目录，则弹出资源列表
          // if (
          //   val === '2' &&
          //   changeFormState.chgType === '4' &&
          //   isNeedOpenResourceList
          // ) {
          //   emit('openResourceList', formState.cataId)
          //   btnLoading.value = false
          //   loading.value = false
          //   return
          // }

          const params = cloneDeep(formState)
          await formRef.value.validate()
          btnLoading.value = true
          loading.value = true

          if (val === '2') {
            await changeFormRef.value.validate()
            params.chgType = changeFormState.chgType
            params.chgDes = changeFormState.chgDes
          }

          if (formState.catalogGroup === '0' && !formState.list.length) {
            proxy.$message.info('请至少添加一条信息项')
            btnLoading.value = false
            loading.value = false
            return
          }

          if (params.catalogGroup === '0') {
            // 如果是常规目录 信息项处理
            params.catalogItems = params.addItemList
            params.catalogItems.forEach((item, index) => {
              delete params.catalogItems[index].id
              delete params.catalogItems[index].dataTypeCode
              delete params.catalogItems[index].isPublicCode
              delete params.catalogItems[index].lastUpdatedBy
            })

            params.editCatalogItems.forEach((item, index) => {
              params.editCatalogItems[index].itemId =
                params.editCatalogItems[index].id
              delete params.editCatalogItems[index].id
              delete params.editCatalogItems[index].dataTypeCode
              delete params.editCatalogItems[index].isPublicCode
              delete params.editCatalogItems[index].lastUpdatedBy
            })
            params.delCatalogItemIds = params.delItemList
          }

          if (
            formState.catalogGroup !== '0' &&
            val !== '0' &&
            !treeList.value.length
          ) {
            proxy.$message.info('请至少添加一条信息项')
            btnLoading.value = false
            loading.value = false
            return
          }

          if (
            ['publishChange', 'publish', 'publishEdit'].includes(type.value) &&
            formState.catalogGroup !== '0'
          ) {
            const checkNodes = inBoxTree.value.getCheckedNodes()
            if (val !== '0' && !checkNodes.length) {
              proxy.$message.info('请选择要发布的库表')
              btnLoading.value = false
              loading.value = false
              return
            }

            const tables = formatParams(cloneDeep(checkNodes))
            // tables.forEach((item) => {
            //   if (selectedResrouces.value.includes(item.tableId)) {
            //     item.isSaveOriginalVersion = '1'
            //   } else {
            //     item.isSaveOriginalVersion = '0'
            //   }
            // })

            params.pdmList = treeList.value.map((item) => {
              return {
                modelId: item.id?.includes('virtual') ? '' : item.id,
                modelName: item.name
              }
            })
            params.tables = tables
          }

          // if (formState.catalogGroup === '0') {
          //   params.catalogItems = formState.list
          // }

          if (params.shardType === '2') {
            params.sharindCondition = params.sharindConditionArr.toString()
          }

          params.fgOpType = val
          params.relateCataId = selectedCatalogs.value?.join(',')

          if (val === '2' && params.chgType === '4') {
            proxy
              .$confirm(
                '取消发布目录将取消目录下挂接的资源以及共享生成的订单，请确认是否继续？',
                '提示',
                {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }
              )
              .then(async () => {
                const { code } =
                  type.value === 'publishEdit'
                    ? await editDataCatalog(params)
                    : type.value === 'publishChange'
                    ? await changeDataCatalog(params)
                    : await registerAndPublishModelCatalog(params)

                if (code === '200') {
                  proxy.$message.success(
                    val === '0'
                      ? '保存成功'
                      : val === '1'
                      ? '发布成功'
                      : '变更提交成功'
                  )
                  // if (['publish'].includes(type.value)) {
                  //   handleChangeTableType()
                  //   handleSubmitChangedFileds()
                  // }
                  handleClose()
                  emit('addCallback')
                } else {
                  proxy.$message.info(message)
                }
                btnLoading.value = false
                loading.value = false
              })
              .catch(() => {
                btnLoading.value = false
                loading.value = false
              })
          } else {
            const { code } =
              type.value === 'publishEdit'
                ? await editDataCatalog(params)
                : type.value === 'publishChange'
                ? await changeDataCatalog(params)
                : await registerAndPublishModelCatalog(params)

            if (code === '200') {
              proxy.$message.success(
                val === '0'
                  ? '保存成功'
                  : val === '1'
                  ? '发布成功'
                  : '变更提交成功'
              )
              // if (['publish'].includes(type.value)) {
              //   handleChangeTableType()
              //   handleSubmitChangedFileds()
              // }
              handleClose()
              emit('addCallback')
            } else {
              proxy.$message.info(message)
            }
            btnLoading.value = false
            loading.value = false
          }
        } catch (e) {
          console.log('e', e)

          btnLoading.value = false
          loading.value = false
        }
      },
      1000,
      {
        leading: true,
        trailing: false
      }
    )

    // 根据选中表格式化数据
    const formatParams = (data) => {
      const arr = data.map((item) => {
        return {
          cataId: formState.cataId,
          tableName: item.enetityCode,
          tableId: item.tableId,
          tableNameCn: item.entityName || item.name,
          tableResType: item.tableResType || '1',
          innerCode: item.innerCode,
          entityId: item.entityId,
          dsId: item.dsId || '',
          tableColumnDtoList: item.tableColumnDtoList,
          factCode: item.factCode,
          factCodeState: item.factCodeState
        }
      })
      arr.forEach((item) => {
        if (item.tableResType === '2') {
          item.tableColumnDtoList = item.tableColumnDtoList.map(
            (val, index) => {
              return {
                ...val,
                colId:
                  publishedCols.value.find((i) => i.itemId === val.itemId)
                    ?.colId || '',
                modelId: val.originModelId,
                innerCode: val.innerCode,
                fieldName: val.itemName,
                fieldCode: val.itemCode,
                fieldType: val.dataType,
                fieldLength: val.dataLength,
                description: val.itemDesc,
                isShare: val.isShare ? '1' : '0',
                isRelCol: val.isRelate ? '1' : '0',
                isBusinessCol: val.isBiz ? '1' : '0',
                fieldIspk: !index ? '1' : '0',
                fieldIsnull: val.isNullable
              }
            }
          )

          item.integrateResRelModelDtoList = item.tableColumnDtoList
            .reduce((pre, cur) => {
              if (
                pre.findIndex((item) => item.innerCode === cur.innerCode) === -1
              ) {
                pre.push(cur)
              }
              return pre
            }, [])
            .map((item) => {
              return {
                modelId: item.originModelId,
                entityId: item.originEntityId,
                innerCode: item.innerCode
              }
            })
        } else {
          item.tableColumnDtoList = item.tableColumnDtoList
            ?.map((val, index) => {
              return {
                ...val,
                fieldName: val.itemName,
                fieldCode: val.itemCode,
                fieldType: val.dataType,
                fieldLength: val.dataLength,
                description: val.itemDesc,
                isShare: val.isShare ? '1' : '0',
                isRelCol: val.isRelate ? '1' : '0',
                isBusinessCol: val.isBiz ? '1' : '0',
                fieldIspk: !index ? '1' : '0',
                fieldIsnull: val.isNullable
              }
            })
            .filter((item) => item.isBind !== '-1')
        }
      })
      return arr
    }

    // 编辑左侧树
    const handleEditInfo = (val) => {
      const virtualInfo = ref('')
      if (virtualTreeList.value && virtualTreeList.value.length) {
        virtualInfo.value = cloneDeep({
          modelName: virtualTreeList.value[0].name,
          modelId: virtualTreeList.value[0].id,
          tableResType: '2',
          entityName:
            virtualTreeList.value[0].children &&
            virtualTreeList.value[0].children.length
              ? virtualTreeList.value[0].children[0].name
              : '',

          enetityCode:
            virtualTreeList.value[0].children &&
            virtualTreeList.value[0].children.length
              ? virtualTreeList.value[0].children[0].enetityCode
              : '',
          selectTableRow:
            virtualTreeList.value[0].children &&
            virtualTreeList.value[0].children.length
              ? virtualTreeList.value[0].children[0].tableColumnDtoList
              : []
        })
      } else {
        virtualInfo.value = ''
      }

      emit(
        'editInfo',
        val,
        val === '2' ? virtualInfo.value : selectedCatalogs.value,
        val === '1'
          ? courseList.value.find((i) => i.systemId === formState.fromSystem)
              ?.unitId
          : ''
      )
    }

    // 切换过业务类型的表
    const selectedTable = ref([])
    // 切换业务表和非业务表
    const handleSeletTable = (data) => {
      if (
        [
          'view',
          'changeView',
          'publishView',
          'catalogRegAudit',
          'audit',
          'catalogPublishAudit',
          'publish',
          'publishEdit'
        ].includes(type.value)
      ) {
        return
      }
      const modelIndex = realTreeList.value.findIndex(
        (item) => item.id === data.modelId
      )
      const tableIndex = realTreeList.value[modelIndex].children.findIndex(
        (item) => item.id === data.id
      )

      if (data.entityCatalogCode === '1') {
        data.entityCatalogCode = ''
        const arr = realTreeList.value[modelIndex].children
        arr[tableIndex].entityCatalogCode = ''
        inBoxTree.value.updateKeyChildren(data.modelId, [...arr])
      } else {
        data.entityCatalogCode = '1'
        const arr = realTreeList.value[modelIndex].children
        arr[tableIndex].entityCatalogCode = '1'
        inBoxTree.value.updateKeyChildren(data.modelId, [...arr])
      }
      const index = selectedTable.value.findIndex((item) => item.id === data.id)
      if (index === -1) {
        selectedTable.value.push({
          id: data.id,
          name: data.name,
          entityCatalogCode: data.entityCatalogCode
        })
      } else {
        selectedTable.value[index].entityCatalogCode = data.entityCatalogCode
      }
    }

    // 提交表类型切换
    const handleChangeTableType = () => {
      if (selectedTable.value && selectedTable.value.length) {
        let entityIdCatalogs = selectedTable.value
          .map((item) => {
            const tempStr = !item.entityCatalogCode ? '0' : '1'
            return item.id + ':' + tempStr
          })
          .toString()
        const formData = new FormData()
        formData.append('entityIdCatalogs', entityIdCatalogs)
        batchModifyEntityCatalog(formData)
      }
    }

    // 提交字段的关联和业务类型切换
    const handleSubmitChangedFileds = () => {
      if (selectedFields.value && selectedFields.value.length) {
        let columnIdBizVals = selectedFields.value
          .map((item) => {
            const tempStr = !item.isBiz ? '0' : '1'
            const tempStr2 = !item.isRelate ? '0' : '1'
            return item.itemId + ':' + tempStr + ':' + tempStr2
          })
          .toString()
        const formData = new FormData()
        formData.append('columnIdBizVals', columnIdBizVals)
        batchModifyColBiz(formData)
      }
    }

    // 获取共享类型
    const shardConditionOpt = ref([])
    const queryShardType = async () => {
      const { code, data } = await proxy.$api.dict.getDictByType({
        type: 'BM_SHARIND_CONDITION'
      })
      if (code === '200') {
        shardConditionOpt.value = Object.entries(data).map(([key, value]) => {
          return {
            label: value,
            value: key
          }
        })
      }
    }

    const dataTypeDict = ref([])
    const queryDataType = async () => {
      const { code, data } = await proxy.$api.dict.getDictByType({
        type: 'BM_DATA_TYPE'
      })
      if (code === '200') {
        dataTypeDict.value = Object.entries(data).map(([key, value]) => {
          return {
            label: value,
            value: key
          }
        })
      }
    }

    // 新增信息项
    const handleAdd = () => {
      emit('openAddInfoItemDrawerCallback', 'add')
    }

    // 信息项按钮组
    const getInfoItemButtons = (row) => {
      return {
        row: row,
        buttons: [
          {
            label: '编辑',
            icon: 'edit',
            clickFn: handleEdit
          },
          {
            label: '删除',
            icon: 'delete',
            clickFn: handleDelete
          }
        ]
      }
    }

    // 信息项编辑弹窗
    const handleEdit = (row) => {
      emit('openAddInfoItemDrawerCallback', 'edit', row.row)
    }
    // 信息项删除
    const handleDelete = ({ row, $index }) => {
      proxy
        .$confirm('您确定要删除该信息项吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          formState.list.splice($index, 1)
          formState.delItemList.push(row.id)
          // 去除掉新增和编辑的信息项集合
          spliceAddOrEditItemList(row.id)
        })
    }

    const spliceAddOrEditItemList = (id) => {
      let indexAdd = formState.addItemList.findIndex((item) => item.id === id)
      let indexEdit = formState.editCatalogItems.findIndex(
        (item) => item.id === id
      )

      if (indexAdd > 0) {
        formState.addItemList.splice(indexAdd, 1)
      }

      if (indexEdit > 0) {
        formState.editCatalogItems.splice(indexEdit, 1)
      }
    }

    const spaceId = ref('')
    const getSpaceId = async () => {
      spaceId.value = config.spaceId
      proxy.$store.commit('setSpaceId', spaceId.value)
      let spaceIdCookies = {}
      spaceIdCookies[proxy.$store.state.user.currentUser.userName] =
        spaceId.value
      Cookies.set('spaceId', JSON.stringify(spaceIdCookies))
      // sessionStorage.setItem('spaceId', spaceId.value)
    }

    // 选择部门
    const handleChooseUnit = () => {
      emit('openAuthorizeUnitDrawer', {
        unitCode: formState.providerCode,
        unitName: formState.providerDeptName,
        unitId: formState.resourceProviderId
      })
    }

    // 处理系统选择变化
    const handleSystemChange = (systemId) => {
      if (!systemId) {
        // 如果清空系统选择，则清空部门信息
        formState.unitName = ''
        formState.providerDeptName = ''
        formState.providerCode = ''
        formState.resourceProviderId = ''
        return
      }
      if (type.value === 'publish') {
        formState.categoryId = ''
        formState.categoryName = ''
        formState.resourceCode = ''
      }
      // 根据选中的系统ID查找对应的系统对象
      const selectedSystem = fromSystemList.value.find(
        (system) => system.systemId === systemId
      )

      if (selectedSystem) {
        // 将系统的部门信息带入到表单中
        if (selectedSystem.unitName) {
          formState.unitName = selectedSystem.unitName
          formState.providerDeptName = selectedSystem.unitName
        }
        if (selectedSystem.unitCode) {
          formState.providerCode = selectedSystem.unitCode
        }
        if (selectedSystem.unitId) {
          formState.resourceProviderId = selectedSystem.unitId
        }

        // 设置系统名称
        formState.systemName = selectedSystem.systemName
      }

      // 如果切换系统 在数据目录发布时需要情况下面的模型字段内容，
      if (type.value === 'publish') {
        realTreeList.value = []
        virtualTreeList.value = []
        defaultSelectedKeys.value = []
        selectedFields.value = []
        selectedCatalogs.value = []
        formState.list = []
      }
    }

    // 获取审核记录
    const auditRecord = ref([])
    const getAuditRecord = async (id) => {
      try {
        const res = await proxy.$api.bizApi.auditing.queryAuditRecord(id)
        if (res.code === '200' || res.code === 200) {
          auditRecord.value = res.data
        }
      } catch (e) {
        console.log(e)
      }
    }

    watch(
      () => activeTab.value,
      (val) => {
        if (val === 'process') {
          nextTick(() => {
            proxy.$refs.progressRef.handleOpened(formState.cataId)
          })
        }
      }
    )

    // 控制快速发布数据库目录时不展示新增按钮
    const isShowAddBtn = ref(true)

    const changeFormRef = ref(null)
    const changeFormState = reactive({
      chgType: '1',
      chgDes: '',
      isSaveOriginalVersion: '0'
    })

    const changeFormRules = {
      chgType: [
        {
          required: true,
          message: '请选择变更类型',
          trigger: 'change'
        }
      ],
      chgDes: [
        {
          required: true,
          message: '请输入变更说明',
          trigger: 'blur'
        }
      ]
    }

    const changeReasonList = ref([
      {
        label: '修改',
        value: '1'
      },
      {
        label: '取消发布',
        value: '4'
      }
    ])

    // 已发布资源列表
    const publishedResourceList = computed(() => {
      const arr = treeList.value
        .reduce((pre, cur) => {
          return pre.concat(cur.children)
        }, [])
        .filter((item) => item.tableId)
      return arr
    })

    const selectedResrouces = ref([])

    const isShowCatalogTypeSwitch = ref(true)

    const handleChangeCatalogGroup = (val) => {
      if (val === '2') {
        formState.list = []
      } else {
        formState.list = []
        realTreeList.value = []
        virtualTreeList.value = []
        selectedCatalogs.value = []
      }
    }

    return {
      isDisabledSystem,
      buttonConfig,
      showOrderedWarning,
      submitBtnDisabled,
      setRowIsShare,
      setTreeChecked,
      handleChangeCatalogGroup,
      displayIntegratedResources,
      isShowCatalogTypeSwitch,
      publishedResourceList,
      selectedResrouces,
      changeReasonList,
      changeFormRef,
      changeFormState,
      changeFormRules,
      isShowAddBtn,
      handleDetail,
      virtualTable,
      handleChangeEntitiesIsDel,
      selectedType,
      selectedObj,
      activeTab,
      auditRecord,
      handleChooseUnit,
      infoMap,
      getAuditRecord,
      type,
      confirmFormRef,
      confirmFormState,
      rulesToConfirm,
      visible,
      formRef,
      handleAdd,
      rules,
      inBoxTree,
      getInfoItemButtons,
      shardConditionOpt,
      formState,
      loading,
      handleEditInfo,
      titleActiveA,
      titleActiveB,
      titleActiveC,
      defaultSelectedKeys,
      disabled,
      showCategory,
      handleOpen,
      handleConfirmDir,
      handleClose,
      openDirectory,
      cleanCategory,
      loadMore,
      filterMethod,
      visibleChange,
      fromSystemList,
      handleToggleSwitch,
      tableRef,
      handleHeaderDrag,
      handleCommand,
      handleNodeClick,
      handleNodeCheck,
      defaultProps,
      treeList,
      realTreeList,
      virtualTreeList,
      handleModuleTable,
      treeLoading,
      infoItemLoading,
      handleIsShare,
      btnLoading,
      save,
      rangeNumber,
      searchLoad,
      systemAdd,
      handleSystemChange,
      handleEditModel,
      handleDeleteTreeNode,
      resetRightTableContent,
      scrollbarRef,
      handleRelateModels,
      updateTreeList,
      getPassStatus,
      updateScrollbar,
      handlePublish,
      handleSeletTable
    }
  }
})
</script>

<style scoped lang="scss">
.fromSystemSelect {
  width: 200px !important;

  /deep/ .el-select-dropdown__item {
    width: 200px !important;
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;

  .deletedClass {
    color: red;
    text-decoration-line: line-through;
  }

  .blueColor {
    color: #1b00a7;
  }
}

.moduleInfo {
  display: flex;
  height: 370px;
  gap: 12px;

  .leftTree {
    display: flex;
    flex-direction: column;
    width: 250px;
    gap: 6px;

    .leftTree-top {
      height: 50px;
      border: 1px solid #ccc;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .leftTree-bottom {
      flex: 1;
      overflow: auto;
      border: 1px solid #ccc;
    }
  }

  .rightTable {
    overflow: auto;
    flex: 1;
  }
}

.infoItem {
  height: 370px;
  display: flex;
  flex-direction: column;
}

/deep/ .el-tree > .el-tree-node > .el-tree-node__content > .is-disabled {
  display: none;
}

/deep/ .tag-info {
  background-color: #f4f4f5;
  border-color: #e9e9eb;
  color: #909399 !important;
}

.circel {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.height100 {
  height: 100%;
}

/deep/ .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 0 !important;
}

/deep/ .scrollbar-wrapper {
  .el-scrollbar__wrap {
    .el-scrollbar__view {
      padding-right: 12px;
    }
  }
}

// 动态颜色按钮悬停效果
.dynamic-color-btn {
  &:hover {
    background-color: var(--btn-color) !important;
    border-color: var(--btn-color) !important;
    opacity: 0.8;
  }

  &:focus {
    background-color: var(--btn-color) !important;
    border-color: var(--btn-color) !important;
    opacity: 0.9;
  }
}
</style>

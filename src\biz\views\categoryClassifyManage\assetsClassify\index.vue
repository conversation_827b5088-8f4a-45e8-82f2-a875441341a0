<template>
  <el-container class="page-container">
    <el-container>
      <el-aside style="width: 360px; background: #fff">
        <!-- 新增按钮 -->
        <div
          class="left-toolbar"
          style="
            padding: 12px;
            border-bottom: 1px solid #ebeef5;
            text-align: center;
          "
        >
          <i
            class="el-icon-plus tree-add-icon"
            title="新增分类"
            @click="handleAdd"
          ></i>
        </div>
        <el-form @submit.native.prevent class="formStyle" inline>
          <el-form-item label="分类名称">
            <el-input
              v-model="filterText"
              placeholder="请输入分类名称或编码"
              clearable
              size="mini"
              :maxlength="50"
              @keyup.enter.native="leftQuery"
              @clear="clearSearch"
            />
          </el-form-item>
          <perm-button
            label="查询"
            type="primary"
            icon="uims-icon-query"
            @click="leftQuery"
          />
        </el-form>
        <el-scrollbar style="height: 100%; flex: 1" ref="scrollbar">
          <div class="tree-container" @click="handleTreeContainerClick">
            <el-tree
              :default-expand-all="treeExpand"
              :default-expanded-keys="[]"
              class="list"
              v-loading="treeLoading"
              :props="defaultProps"
              :data="treeList"
              node-key="id"
              highlight-current
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              @node-click="handleNodeClick"
              ref="inBoxTree"
            >
              <span slot-scope="{ node, data }" class="tree-node">
                <span class="tree-node-content">
                  <i
                    v-if="data.children && data.children.length > 0"
                    :class="[
                      'tree-expand-icon',
                      node.expanded
                        ? 'el-icon-caret-bottom'
                        : 'el-icon-caret-right'
                    ]"
                    @click.stop="toggleNodeExpand(node)"
                    title="展开/收缩"
                  ></i>
                  <span
                    v-else
                    class="tree-expand-icon tree-leaf-placeholder"
                  ></span>
                  <span :title="node.label" class="tree-node-label">
                    {{ node.label }}
                  </span>
                </span>
                <span class="tree-node-actions">
                  <i
                    class="el-icon-edit tree-node-icon"
                    title="编辑分类"
                    @click.stop="handleEditTreeNode(data)"
                  ></i>
                  <i
                    class="el-icon-delete tree-node-icon delete-icon"
                    title="删除分类"
                    @click.stop="handleDeleteTreeNode(data)"
                  ></i>
                </span>
              </span>
            </el-tree>
          </div>
        </el-scrollbar>
      </el-aside>
      <el-container>
        <el-header>
          <!--列表查询区-->
          <el-form
            :inline="true"
            :model="filters"
            :size="size"
            @submit.native.prevent
          >
            <el-form-item label="资产分类名称">
              <el-input
                @change="handleFilter"
                style="width: 300px"
                v-model="filters.name"
                placeholder="请输入资产分类名称"
                clearable
                :maxlength="50"
              />
            </el-form-item>

            <perm-button
              label="查询"
              type="primary"
              icon="uims-icon-query"
              @click="handleFilter"
              style="margin-right: 10px"
            />
            <perm-button
              label="重置"
              type="primary"
              icon="uims-icon-reset"
              @click="resetFilter"
            />
          </el-form>
        </el-header>
        <!--列表表格区-->
        <el-main>
          <table-plus
            @header-dragend="handleHeaderDrag"
            id="assetClassifyTable"
            :data="list"
            ref="multipleTable"
            border
            fit
            default-expand-all
            v-loading="listLoading"
            tooltip-effect="light"
            stripe
            highlight-current
            highlight-current-row
          >
            <el-table-column
              prop="name"
              label="分类名称"
              show-overflow-tooltip
              min-width="200"
            ></el-table-column>
            <el-table-column
              prop="modifyTime"
              label="更新时间"
              align="center"
              show-overflow-tooltip
              width="180"
            ></el-table-column>
            <el-table-column
              prop="isEnabledCn"
              align="center"
              label="状态"
              show-overflow-tooltip
              width="100"
            >
              <template slot-scope="{ row }">
                <el-tag :type="row.isEnabled === '1' ? 'success' : 'danger'">
                  {{ row.isEnabled === '1' ? '上线' : '下线' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="流程图"
              header-align="center"
              align="center"
              width="120"
            >
              <template slot-scope="{ row }">
                <div class="image-preview">
                  <el-image
                    v-if="hasImage(row)"
                    :src="getImageDisplayUrl(row)"
                    :preview-src-list="[getImageDisplayUrl(row)]"
                    fit="cover"
                    style="width: 60px; height: 40px; border-radius: 4px"
                  >
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                  <div v-else class="no-image">
                    <i class="el-icon-picture-outline"></i>
                    <span>暂无图片</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              header-align="center"
              show-overflow-tooltip
              min-width="150"
            ></el-table-column>
            <el-table-column
              label="操作"
              align="left"
              fixed="right"
              width="270"
              class-name="smart_city-table-operate-cell"
            >
              <template slot-scope="{ row }">
                <perm-button-group :config="getButtons(row)" />
              </template>
            </el-table-column>
          </table-plus>
        </el-main>
        <el-footer>
          <table-footer
            ref="tableFooter"
            :showToolBar="true"
            :showPage="true"
            :tableRef="this.$refs.multipleTable"
            @sizeChange="handleSizeChange"
            @currentChange="handleCurrentChange"
            :currentPage="filters.currentPage"
            :pageSizes="[10, 20, 50, 100]"
            :pageSize="filters.pageSize"
            :total="total"
          >
          </table-footer>
        </el-footer>
      </el-container>
    </el-container>

    <!-- 资产分类弹窗 -->
    <AssetClassifyDialog
      ref="AssetClassifyDialog"
      @update="update"
      @updateTree="handleUpdateTree"
    ></AssetClassifyDialog>
  </el-container>
</template>

<script>
import PermButton from '@/core/components/PermButton'
import TablePlus from '@/core/components/TablePlus'
import TableFooter from '@/core/components/TablePlus/TableFooter'
import PermButtonGroup from '@/core/components/PermButtonGroup'
import AssetClassifyDialog from './addDialog'

export default {
  name: 'AssetClassifyIndex',
  components: {
    TablePlus,
    PermButton,
    TableFooter,
    PermButtonGroup,
    AssetClassifyDialog
  },
  data() {
    return {
      systemCode: config.systemCode,
      currentClickNodeIds: [],
      filterText: undefined,
      currentSelectInfo: undefined,
      size: 'mini',
      list: [],
      listLoading: true,
      treeLoading: true,
      total: 0,
      lazy: false,
      treeExpand: false,
      currentRow: undefined,
      defaultCurrentId: '',
      treeList: [],
      defaultProps: {
        children: 'children',
        label: 'name',
        id: 'id',
        isLeaf: (data, node) => {
          // 控制分类层级只有四层：一级、二级、三级、四级
          // 如果当前节点是四级分类（level >= 3），则设为叶子节点
          return node.level >= 3 || !(data.childes || data.childes > 0)
        }
      },

      filters: {
        currentPage: 1,
        pageSize: 20,
        name: '',
        pid: ''
      },
      searchTimer: null, // 搜索防抖定时器
      searchState: null // 保存搜索前的状态
    }
  },
  methods: {
    // 获取按钮配置
    getButtons(row) {
      return {
        row: row,
        buttons: [
          // {
          //   label: '查看',
          //   icon: 'view',
          //   clickFn: this.handleView
          // },
          {
            label: row.isEnabled === '1' ? '下线' : '上线',
            icon: row.isEnabled === '1' ? 'disable' : 'enable',
            clickFn: this.handleStatusChange,
            type: 'status'
          },
          {
            label: '编辑',
            icon: 'edit',
            clickFn: this.handleEdit
          },
          {
            label: '删除',
            icon: 'cancel',
            type: 'cancel',
            clickFn: this.handleDelete
          }
        ],
        showNums: 4
      }
    },

    // 监听表头拖拽事件
    handleHeaderDrag(newWidth, oldWidth, column, event) {
      this.$nextTick(() => {
        this.$refs.multipleTable.doLayout()
      })
    },
    // 查询过滤
    handleFilter() {
      console.log(1213)

      this.$nextTick(() => {
        console.log(1213)

        this.filters.currentPage = 1
        this.getList()
      })
    },
    // 重置过滤
    resetFilter() {
      this.filters.currentPage = 1
      this.filters.pageSize = 20
      this.filters.name = ''
      this.filters.code = ''
      // this.filters.pid = ''

      this.getList()
    },
    // 获取树形数据
    async getInBoxTree() {
      this.treeLoading = true
      try {
        const res = await this.$api.bizApi.assetClassify.assetsClassifyTree()
        if (res.code === '200') {
          if (res.data && res.data.length > 0) {
            res.data.sort((a, b) => a.sort - b.sort)
            this.treeList = res.data
            // this.defaultCurrentId = res.data[0].id
            // this.currentSelectInfo = res.data[0]
            // this.filters.pid = res.data[0].id

            this.$nextTick(() => {
              // 设置当前选中的节点
              this.$refs['inBoxTree'].setCurrentKey(this.defaultCurrentId)
              // 设置默认展开的第一个节点
              const firstNode = this.$refs['inBoxTree'].getNode(res.data[0].id)
              if (
                firstNode &&
                firstNode.childNodes &&
                firstNode.childNodes.length > 0
              ) {
                firstNode.expand()
              }
            })

            this.getList()
            this.nodeClick()
          } else {
            this.treeList = []
            this.currentSelectInfo = null
            this.list = []
            this.total = 0
          }
        } else {
          this.$message.error(res.message || '获取分类树失败')
        }
      } catch (error) {
        console.error('获取分类树失败:', error)
        this.$message.error('获取分类树失败，请稍后重试')
      } finally {
        this.treeLoading = false
        this.listLoading = false
      }
    },

    // 节点点击
    handleNodeClick(item, node) {
      // 如果点击的是当前已选中的节点，取消选中状态，但右侧数据保持不变
      if (this.currentSelectInfo && this.currentSelectInfo.id === item.id) {
        // 取消选中状态
        this.currentSelectInfo = null
        this.$refs.inBoxTree.setCurrentKey(null)
        // 清空查询条件中的父级ID，但不清空右侧列表数据
        this.filters.pid = ''
        return
      }

      this.currentClickNodeIds = []
      const convert = (node) => {
        if (node.data && node.data.id) {
          this.currentClickNodeIds.unshift(node.data.id)
          if (node.parent) {
            convert(node.parent)
          }
        }
      }
      convert(node)
      this.currentSelectInfo = item
      this.filters.pid = item.id
      this.getList()
      this.nodeClick()
    },

    // 切换节点展开/收缩状态
    toggleNodeExpand(node) {
      if (node.expanded) {
        node.collapse()
      } else {
        node.expand()
      }
    },
    // 获取列表数据
    async getList() {
      try {
        this.listLoading = true
        const res = await this.$api.bizApi.assetClassify.assetClassifyList(
          this.filters
        )
        if (res.code === '200') {
          if (res.data.total > 0) {
            res.data.records.sort((a, b) => a.sort - b.sort)
          }
          this.listLoading = false
          this.list = res.data.records
          this.total = res.data.total
        }
      } catch (e) {
        this.list = []
        this.total = 0
      } finally {
        this.listLoading = false
      }
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.filters.pageSize = val
      this.getList()
    },
    // 当前页改变
    handleCurrentChange(val) {
      this.filters.currentPage = val
      this.getList()
    },
    // 新增
    handleAdd() {
      // 根据当前选中的分类情况决定新增方式
      if (!this.currentSelectInfo) {
        // 没有选中分类，新增一级分类
        this.$refs.AssetClassifyDialog.open('add', null, null)
      } else {
        // 选中了分类，判断当前分类的层级
        const currentLevel = this.getCurrentCategoryLevel(
          this.currentSelectInfo
        )

        if (currentLevel >= 4) {
          // 如果选中的是四级分类，不能再新增子分类
          this.$notify({
            title: '提示',
            message: '最多支持四级分类，无法在此分类下新增子分类',
            type: 'warning',
            duration: 3000
          })
          return
        }

        // 新增子分类
        this.$refs.AssetClassifyDialog.open('add', this.currentSelectInfo, null)
      }
    },
    // 获取当前分类的层级
    getCurrentCategoryLevel(categoryInfo) {
      // 使用 Element UI Tree 组件的官方方法获取节点层级
      const node = this.$refs.inBoxTree?.getNode(categoryInfo.id)
      return node ? node.level : 1 // 默认返回一级
    },
    // 查看
    handleView(row) {
      // 获取要查看的分类的父级分类信息
      const parentInfo = this.getParentInfo(row)
      this.$refs.AssetClassifyDialog.open('view', parentInfo, row)
    },
    // 编辑
    handleEdit(row) {
      // 获取要编辑的分类的父级分类信息
      const parentInfo = this.getParentInfo(row)
      this.$refs.AssetClassifyDialog.open('edit', parentInfo, row)
    },
    // 状态变更
    handleStatusChange(row) {
      const action = row.isEnabled === '1' ? '下线' : '上线'
      this.$confirm(`将${action}选择内容，请确认是否继续？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调整参数格式：使用 assetTypeIds 数组和 optOnlineStatus 字符串
        const optOnlineStatus = row.isEnabled === '1' ? '0' : '1' // 1--上线 0--下线

        this.$api.bizApi.assetClassify
          .assetsClassifyOnOrOffline({
            assetTypeIds: [row.id], // 分类集合
            optOnlineStatus: optOnlineStatus // 1--上线 0--下线
          })
          .then((res) => {
            if (res.code === '200') {
              this.$notify({
                title: '提示',
                message: `${action}成功`,
                type: 'success',
                duration: 3000
              })
              this.getList()
              this.updateTreeWithState() // 更新树形结构，保持展开状态
            } else {
              this.$message.error(res.message || `${action}失败`)
            }
          })
      })
    },
    // 删除
    handleDelete(row) {
      let confirmMessage = `将删除《${row.name}》分类，且删除后不支持撤销，请确认是否继续？`

      this.$confirm(confirmMessage, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.bizApi.assetClassify
          .deleteAssetsClassify({ assetTypeIds: [row.id] })
          .then((res) => {
            if (res.code === '200') {
              this.$notify({
                title: '提示',
                message: '删除成功',
                type: 'success',
                duration: 3000
              })
              this.getList()
              this.updateTreeWithState() // 更新树形结构，保持展开状态
            } else {
              this.$message.error(res.message || '删除失败')
            }
          })
      })
    },
    // 左侧结构查询
    leftQuery() {
      // 如果是第一次搜索，保存当前状态
      if (this.filterText && this.filterText.trim() && !this.searchState) {
        this.saveSearchState()
      }

      // 触发树的过滤
      this.$refs.inBoxTree.filter(this.filterText)

      // 如果有搜索内容，展开所有节点以显示搜索结果
      if (this.filterText && this.filterText.trim()) {
        this.$nextTick(() => {
          // 展开所有匹配的节点
          this.expandMatchedNodes()
          // 保持选中状态
          if (this.currentSelectInfo && this.currentSelectInfo.id) {
            this.$refs.inBoxTree.setCurrentKey(this.currentSelectInfo.id)
          }
        })
      } else {
        // 如果没有搜索内容，恢复搜索前的状态
        if (this.searchState) {
          this.restoreSearchState()
          this.searchState = null // 清空保存的状态
        } else {
          // 如果没有保存的状态，恢复默认状态
          this.restoreDefaultExpansion()
          this.restoreSelection()
        }
      }
    },

    // 清空搜索
    clearSearch() {
      this.filterText = ''

      // 先清空过滤，让所有节点重新显示
      this.$refs.inBoxTree.filter('')

      // 延迟恢复状态，确保过滤清空完成
      setTimeout(() => {
        // 恢复搜索前的状态
        if (this.searchState) {
          this.restoreSearchState()
          // 延迟清空保存的状态，确保恢复完成
          setTimeout(() => {
            this.searchState = null
          }, 1000)
        } else {
          // 如果没有保存的状态，恢复默认状态
          this.restoreDefaultExpansion()
          this.restoreSelection()
        }
      }, 200)
    },

    // 恢复默认展开状态
    restoreDefaultExpansion() {
      this.$nextTick(() => {
        // 折叠所有节点
        const allNodes = this.$refs.inBoxTree.store.nodesMap
        Object.keys(allNodes).forEach((key) => {
          const node = allNodes[key]
          if (node.expanded) {
            node.collapse()
          }
        })

        // 注意：展开状态现在通过 getAllExpandedNodeIds 和 restoreAllExpandedStates 方法管理
      })
    },

    // 恢复选中状态
    restoreSelection() {
      this.$nextTick(() => {
        if (this.currentSelectInfo && this.currentSelectInfo.id) {
          this.$refs.inBoxTree.setCurrentKey(this.currentSelectInfo.id)
        }
      })
    },

    // 获取当前所有展开的节点ID（包括所有层级）
    getAllExpandedNodeIds() {
      const expandedIds = []
      if (!this.$refs.inBoxTree || !this.$refs.inBoxTree.store) {
        return expandedIds
      }

      const traverse = (node) => {
        if (node.expanded) {
          expandedIds.push(node.key)
        }
        if (node.childNodes && node.childNodes.length > 0) {
          node.childNodes.forEach((child) => traverse(child))
        }
      }

      if (
        this.$refs.inBoxTree.store.root &&
        this.$refs.inBoxTree.store.root.childNodes
      ) {
        this.$refs.inBoxTree.store.root.childNodes.forEach((node) =>
          traverse(node)
        )
      }

      return expandedIds
    },

    // 恢复所有层级的展开状态
    restoreAllExpandedStates(expandedIds) {
      if (!expandedIds || expandedIds.length === 0) return

      this.$nextTick(() => {
        expandedIds.forEach((nodeId) => {
          const node = this.$refs.inBoxTree?.getNode(nodeId)
          if (node && !node.expanded) {
            node.expand()
          }
        })
      })
    },

    // 保存展开状态，更新数据，恢复展开状态
    async updateTreeWithState() {
      try {
        // 保存当前所有展开的节点ID
        const allExpandedIds = this.getAllExpandedNodeIds()
        const currentSelectedId = this.currentSelectInfo?.id

        const res = await this.$api.bizApi.assetClassify.assetsClassifyTree()
        if (res.code === '200' && res.data && res.data.length > 0) {
          res.data.sort((a, b) => a.sort - b.sort)

          // 更新树数据
          this.treeList = res.data

          // 恢复所有展开状态
          this.restoreAllExpandedStates(allExpandedIds)

          // 恢复选中状态
          this.$nextTick(() => {
            if (currentSelectedId) {
              this.$refs.inBoxTree.setCurrentKey(currentSelectedId)
              const selectedNode =
                this.$refs.inBoxTree.getNode(currentSelectedId)
              if (selectedNode && selectedNode.data) {
                this.currentSelectInfo = selectedNode.data
              }
            }
          })
        }
      } catch (error) {
        console.error('更新树数据失败:', error)
      }
    },

    // 节点点击后的处理
    nodeClick() {
      let num = 0
      let timer = setInterval(() => {
        num += 1
        this.$refs.scrollbar.update()
        if (num === 3) {
          clearInterval(timer)
        }
      }, 300)
    },

    // 过滤节点
    filterNode(value, data) {
      if (!value) return true

      // 支持分类名称和编码的搜索
      const searchValue = value.toLowerCase()
      const name = (data.name || '').toLowerCase()
      const code = (data.code || '').toLowerCase()

      // 检查当前节点是否匹配
      const currentMatch =
        name.includes(searchValue) || code.includes(searchValue)

      // 检查子节点是否有匹配的（递归搜索）
      const hasMatchingChild = this.hasMatchingChild(data, searchValue)

      return currentMatch || hasMatchingChild
    },

    // 检查是否有匹配的子节点
    hasMatchingChild(node, searchValue) {
      if (!node.children || node.children.length === 0) {
        return false
      }

      return node.children.some((child) => {
        const name = (child.name || '').toLowerCase()
        const code = (child.code || '').toLowerCase()
        const childMatch =
          name.includes(searchValue) || code.includes(searchValue)

        // 递归检查子节点的子节点
        const hasGrandChild = this.hasMatchingChild(child, searchValue)

        return childMatch || hasGrandChild
      })
    },

    // 展开匹配的节点
    expandMatchedNodes() {
      if (!this.filterText || !this.filterText.trim()) {
        return
      }

      const searchValue = this.filterText.toLowerCase()
      const expandKeys = []

      // 递归查找需要展开的节点
      const findExpandKeys = (nodes, parentKey = null) => {
        nodes.forEach((node) => {
          const name = (node.name || '').toLowerCase()
          const code = (node.code || '').toLowerCase()
          const hasMatch =
            name.includes(searchValue) || code.includes(searchValue)

          if (hasMatch && parentKey) {
            expandKeys.push(parentKey)
          }

          if (node.children && node.children.length > 0) {
            const hasChildMatch = this.hasMatchingChild(node, searchValue)
            if (hasChildMatch) {
              expandKeys.push(node.id)
            }
            findExpandKeys(node.children, node.id)
          }
        })
      }

      findExpandKeys(this.treeList)

      // 展开匹配的节点
      expandKeys.forEach((key) => {
        const node = this.$refs.inBoxTree.getNode(key)
        if (node && !node.expanded) {
          node.expand()
        }
      })
    },

    // 保存搜索前的状态
    saveSearchState() {
      const expandedKeys = this.getAllExpandedNodeIds()
      this.searchState = {
        selectedId: this.currentSelectInfo ? this.currentSelectInfo.id : null,
        expandedKeys: expandedKeys
      }
    },

    // 恢复搜索前的状态
    restoreSearchState() {
      if (this.searchState) {
        // 先恢复展开状态，再恢复选中状态
        this.restoreAllExpandedStates(this.searchState.expandedKeys)

        // 使用 nextTick 而不是延时，减少等待时间
        this.$nextTick(() => {
          if (this.searchState && this.searchState.selectedId) {
            this.$refs.inBoxTree.setCurrentKey(this.searchState.selectedId)
          }
        })
      }
    },
    // 更新
    update() {
      this.updateTreeWithState() // 更新树结构并保持状态
      this.getList() // 更新列表
    },
    // 处理树更新事件
    handleUpdateTree() {
      this.updateTreeWithState()
    },

    // 处理树容器点击事件（实现取消选中）
    handleTreeContainerClick(event) {
      // // 如果点击的是树容器本身（空白区域），取消选中
      // if (
      //   event.target.classList.contains('tree-container') ||
      //   event.target.classList.contains('el-scrollbar__view') ||
      //   event.target.classList.contains('list')
      // ) {
      //   this.handleClearSelection()
      // }
    },
    // 清空选中状态
    handleClearSelection() {
      this.currentSelectInfo = null
      this.$refs.inBoxTree.setCurrentKey(null)
      // 清空右边表格
      this.list = []
      this.total = 0
      // 重置查询条件
      this.filters = {
        currentPage: 1,
        pageSize: 20,
        name: '',
        code: ''
      }
    },
    // 新增一级分类
    handleAddRootCategory() {
      this.$refs.AssetClassifyDialog.open('add', null, null)
    },
    // 新增分类（左侧图标点击）
    handleAddCategory() {
      if (!this.currentSelectInfo) {
        // 没有选中任何分类，新增一级分类
        this.handleAddRootCategory()
      } else {
        // 有选中分类，新增子分类
        this.$refs.AssetClassifyDialog.open('add', this.currentSelectInfo)
      }
    },

    // 编辑分类（左侧图标点击）
    handleEditCategory() {
      if (!this.currentSelectInfo) {
        this.$message.warning('请先选择要编辑的分类')
        return
      }

      // 根据当前选中的分类，确定其父级分类信息
      let parentInfo = this.getParentInfo(this.currentSelectInfo)

      this.$refs.AssetClassifyDialog.open(
        'edit',
        parentInfo,
        this.currentSelectInfo
      )
    },
    // 获取父级分类信息的通用方法
    getParentInfo(currentInfo) {
      // 基于树形结构查找父级节点
      const findParent = (nodes, targetId, parent = null) => {
        for (const node of nodes) {
          if (node.id === targetId) {
            return parent
          }
          if (node.children && node.children.length > 0) {
            const result = findParent(node.children, targetId, node)
            if (result !== null) {
              return result
            }
          }
        }
        return null
      }

      return findParent(this.treeList, currentInfo.id)
    },
    // 新增子分类
    handleAddSubCategory(data) {
      this.$refs.AssetClassifyDialog.open('add', data)
    },
    // 编辑树节点
    handleEditTreeNode(data) {
      // 根据当前选中的分类，确定其父级分类信息
      let parentInfo = this.getParentInfo(data)
      this.$refs.AssetClassifyDialog.open('edit', parentInfo, data)
    },
    // 删除树节点
    handleDeleteTreeNode(data) {
      // 检查是否有子分类
      const hasChildren =
        (data.children && data.children.length > 0) ||
        (data.childes && data.childes > 0)

      let confirmMessage = `将删除《${data.name}》分类，且删除后不支持撤销，请确认是否继续？`
      if (hasChildren) {
        confirmMessage = `将删除《${data.name}》分类及其所有子分类，且删除后不支持撤销，请确认是否继续？`
      }

      this.$confirm(confirmMessage, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.bizApi.assetClassify
          .deleteAssetsClassify({ assetTypeIds: [data.id] })
          .then((res) => {
            if (res.code === '200') {
              this.$notify({
                title: '提示',
                message: '删除成功',
                type: 'success',
                duration: 3000
              })
              this.updateTreeWithState() // 更新树形结构，保持展开状态

              // 检查删除的节点与当前选中节点的关系
              if (this.currentSelectInfo) {
                if (this.currentSelectInfo.id === data.id) {
                  // 如果删除的是当前选中的节点，清空右侧列表
                  this.currentSelectInfo = null
                  this.list = []
                  this.total = 0
                } else if (
                  this.isChildOfNode(data.id, this.currentSelectInfo.id)
                ) {
                  // 如果删除的是当前选中节点的子节点，重新加载右侧数据
                  this.getList()
                } else if (
                  this.isChildOfNode(this.currentSelectInfo.id, data.id)
                ) {
                  // 如果删除的是当前选中节点的父节点，当前选中节点也被删除了，清空右侧列表
                  this.currentSelectInfo = null
                  this.list = []
                  this.total = 0
                }
              }
            } else {
              this.$message.error(res.message || '删除失败')
            }
          })
      })
    },

    // 判断一个节点是否是另一个节点的子节点
    isChildOfNode(childId, parentId) {
      // 使用 Element UI Tree 组件的官方方法获取节点
      const parentNode = this.$refs.inBoxTree?.getNode(parentId)
      const childNode = this.$refs.inBoxTree?.getNode(childId)

      if (!parentNode || !childNode) return false

      // 检查子节点是否在父节点的子树中
      let currentNode = childNode.parent
      while (currentNode) {
        if (currentNode.key === parentId) {
          return true
        }
        currentNode = currentNode.parent
      }

      return false
    },

    // 获取图片URL
    getImageUrl(imageId) {
      return `/api/${config.appCode}/attachment/preview?attachId=${imageId}`
    },

    // 获取图片显示地址（优先使用imageUrl，其次使用imageId构建）
    getImageDisplayUrl(row) {
      return this.getImageUrl(row.imageId)
    },

    // 检查是否有图片
    hasImage(row) {
      return !!(row.imageUrl || row.imageId)
    }
  },
  watch: {
    // 监听搜索框内容变化，实现实时搜索
    filterText() {
      // 防抖处理，避免频繁搜索
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.leftQuery()
      }, 300)
    }
  },
  created() {
    this.getInBoxTree()
  },
  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
}
</script>

<style scoped lang="scss">
.formStyle {
  display: flex;
  align-items: center;
  padding: 4px;
  border-bottom: 1px solid #ccc;

  /deep/ .el-form-item {
    margin-right: 10px;
    margin-bottom: initial;
  }
}

.left-toolbar {
  .tree-add-icon {
    font-size: 18px;
    cursor: pointer;
    color: #409eff;
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 4px;
    background-color: transparent;

    &:hover {
      color: #66b1ff;
      background-color: #ecf5ff;
      transform: scale(1.1);
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.tree-toolbar {
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f5f7fa;
  text-align: center;

  .tree-icon {
    font-size: 16px;
    margin: 0 8px;
    cursor: pointer;
    color: #409eff;
    transition: color 0.3s;

    &:hover {
      color: #66b1ff;
    }

    &.disabled {
      color: #c0c4cc;
      cursor: not-allowed;
    }

    &.delete-icon {
      color: #f56c6c;

      &:hover {
        color: #f78989;
      }

      &.disabled {
        color: #c0c4cc;
      }
    }
  }
}

.tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;

  .tree-node-content {
    flex: 1;
    display: flex;
    align-items: center;
    overflow: hidden;

    .tree-expand-icon {
      font-size: 14px;
      margin-right: 8px;
      margin-left: 4px;
      cursor: pointer;
      color: #c0c4cc;
      transition: all 0.3s ease;
      padding: 2px;
      border-radius: 2px;
      width: 16px;
      text-align: center;

      &:hover {
        color: #409eff;
        background-color: #ecf5ff;
      }

      &.tree-leaf-placeholder {
        cursor: default;

        &:hover {
          color: transparent;
          background-color: transparent;
        }
      }
    }

    .tree-node-label {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .tree-node-actions {
    display: none;
    margin: 0 8px;

    .tree-node-icon {
      font-size: 14px;
      margin-left: 6px;
      cursor: pointer;
      color: #909399;
      transition: all 0.3s ease;
      padding: 4px;
      border-radius: 4px;
      background-color: transparent;

      &:hover {
        color: #409eff;
        background-color: #ecf5ff;
        transform: scale(1.1);
        box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
      }

      &.delete-icon {
        color: #909399;

        &:hover {
          color: #f56c6c;
          background-color: #fef0f0;
          transform: scale(1.1);
          box-shadow: 0 2px 4px rgba(245, 108, 108, 0.2);
        }
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  &:hover {
    .tree-node-actions {
      display: inline-block;
    }
  }
}

.tree-container {
  min-height: 100%;
  position: relative;
}

/deep/ .el-tree-node__content {
  margin: 1px 0;

  &:hover {
    background-color: #f5f7fa;
  }
}

/deep/ .el-tree-node__content > span {
  overflow: hidden;
  text-overflow: ellipsis;
}

// 隐藏Element UI自带的展开图标
/deep/ .el-tree-node__expand-icon {
  display: none;
}

// 图片预览样式
.image-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px 0;
  .no-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #c0c4cc;
    font-size: 12px;

    i {
      font-size: 20px;
      margin-bottom: 4px;
    }

    span {
      font-size: 10px;
    }
  }

  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 14px;
  }
}
</style>
